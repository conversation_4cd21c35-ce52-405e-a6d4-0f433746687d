{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_BatchImport", "_question", "_questionBank", "_methods", "name", "components", "QuestionCard", "QuestionForm", "BatchImport", "data", "_ref", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "selectedQuestions", "_defineProperty2", "default", "reverse", "allowDuplicate", "process", "env", "VUE_APP_BASE_API", "Authorization", "$store", "getters", "token", "watch", "documentContent", "handler", "newVal", "isSettingFromBackend", "console", "log", "substring", "trim", "debounceParseDocument", "parsedQuestions", "parseErrors", "immediate", "importDrawerVisible", "_this", "$nextTick", "initRichEditor", "rich<PERSON><PERSON><PERSON>", "destroy", "editorInitialized", "created", "initPage", "debounce", "parseDocument", "uploadData", "uploadHeaders", "mounted", "loadCachedContent", "window", "addEventListener", "handleBeforeUnload", "<PERSON><PERSON><PERSON><PERSON>", "saveToCacheNow", "autoSaveTimer", "clearTimeout", "removeEventListener", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImport", "batchImportVisible", "handleAddQuestion", "type", "currentQuestionType", "currentQuestionData", "questionFormVisible", "toggleExpandAll", "expandedQuestions", "handleExportQuestions", "length", "warning", "info", "concat", "handleToggleSelectAll", "isAllSelected", "map", "q", "questionId", "success", "handleBatchDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "deletePromises", "delQuestion", "Promise", "all", "allSelected", "_this5", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "handleEditQuestion", "question", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this6", "replace", "displayContent", "handleQuestionFormSuccess", "handleBatchImportSuccess", "handleDrawerClose", "done", "showDocumentImportDialog", "_this7", "isUploading", "isParsing", "uploadComponent", "$refs", "documentUpload", "clearFiles", "documentImportDialogVisible", "showRulesDialog", "activeRuleTab", "rulesDialogVisible", "copyExampleToEditor", "_this8", "htmlTemplate", "setData", "downloadExcelTemplate", "download", "downloadWordTemplate", "beforeUpload", "file", "size", "isValidType", "endsWith", "isLt10M", "uploadUrl", "handleUploadSuccess", "_this9", "code", "setTimeout", "questions", "collapsed", "allExpanded", "errors", "errorCount", "errorDetails", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentHtmlContent", "lastSaveTime", "Date", "toLocaleString", "msg", "handleUploadError", "collapseAll", "_this0", "$set", "toggleQuestion", "toggleAllQuestions", "_this1", "confirmImport", "_this10", "importQuestions", "_this11", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionsToImport", "importData", "_t", "w", "_context", "n", "p", "_toConsumableArray2", "importOptions", "batchImportQuestions", "v", "Error", "clearCache", "a", "_this12", "CKEDITOR", "warn", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "innerHTML", "showFallbackEditor", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "pluginsLoaded", "instanceReady", "editor", "evt", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "removeDialogTabs", "fallback<PERSON><PERSON>r", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "stripHtmlTagsKeepImages", "hasUnsavedChanges", "saveToCache", "contentToLoad", "_this13", "textarea", "createElement", "className", "placeholder", "value", "style", "cssText", "target", "append<PERSON><PERSON><PERSON>", "stripHtmlTags", "html", "div", "textContent", "innerText", "content", "func", "wait", "timeout", "executedFunction", "_len", "arguments", "args", "Array", "_key", "later", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "message", "lines", "split", "line", "filter", "slice", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "join", "parsedQuestion", "parseQuestionFromLines", "_parsedQuestion$quest", "_parsedQuestion$optio", "options", "answer", "<PERSON><PERSON><PERSON><PERSON>", "_parsedQuestion$quest2", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "inferQuestionType", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "finalContent", "hasQuestionNumber", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "result", "_this14", "images", "imageIndex", "contentWithPlaceholders", "img", "startIndex", "isArray", "optionMatch", "optionKey", "toUpperCase", "optionContent", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "f", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "pattern", "matches", "lastMatch", "answerText", "trimmedAnswer", "splitByQuestionType", "sections", "typeRegex", "lastIndex", "currentType", "exec", "parseSectionQuestions", "section", "_this15", "convertQuestionType", "questionBlocks", "splitByQuestionNumber", "block", "parseQuestionBlock", "blocks", "numberRegex", "firstLine", "currentLineIndex", "numberMatch", "parseOptions", "nextIndex", "parseQuestionMeta", "currentIndex", "parseAnswer", "extractAnswerFromContent", "bracketPatterns", "_i4", "_bracketPatterns", "matchAll", "getQuestionTypeName", "_typeof2", "getQuestionTypeNameSafe", "hasOptions", "optionCount", "hasJudgmentKeywords", "_iterator2", "_step2", "getQuestionTypeColor", "colorMap", "cachedData", "localStorage", "getItem", "cache<PERSON>ey", "JSON", "parse", "_this16", "dataToSave", "timestamp", "now", "setItem", "stringify", "removeItem", "event", "returnValue", "manualSave", "handleCacheCommand", "command", "_this17", "exportDraft", "blob", "Blob", "url", "URL", "createObjectURL", "link", "href", "toISOString", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "cleanupQuestionContent", "plainContent", "plainText", "paragraphs", "_iterator3", "_step3", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"importDrawerVisible = true\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <span class=\"orange\">最后保存的草稿时间：{{ lastSaveTime || '暂无' }}</span>\n            <span v-if=\"hasUnsavedChanges\" class=\"unsaved-indicator\">●</span>\n            <div class=\"fr\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                @click=\"manualSave\"\n                :disabled=\"!hasUnsavedChanges\"\n              >\n                <i class=\"el-icon-document\"></i> 保存草稿\n              </el-button>\n              <el-dropdown trigger=\"click\" @command=\"handleCacheCommand\">\n                <el-button size=\"mini\" type=\"info\">\n                  <i class=\"el-icon-more\"></i>\n                </el-button>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item command=\"clear\">清除缓存</el-dropdown-item>\n                  <el-dropdown-item command=\"export\">导出草稿</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeNameSafe(question) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 题干内容 -->\n                    <div class=\"question-main-content\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n\n                    <!-- 答案显示在下方 -->\n                    <div class=\"question-answer-section\">\n                      <span class=\"question-answer-label\">答案：</span>\n                      <span class=\"question-answer-value\">{{ question.correctAnswer }}</span>\n                    </div>\n                  </div>\n\n                  <!-- 解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button>\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx/.xlsx格式的文件<br>\n          2.Word导入支持全部题型，Excel导入不支持完形填空题、组合题<br>\n          3.Word导入支持导入图片/公式，Excel导入暂不支持<br>\n          4.题目数量过多、题目文件过大（如图片较多）等情况建议分批导入<br>\n          5.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      lastSaveTime: '',\n      // 缓存相关\n      cacheKey: 'questionBank_draft_content',\n      autoSaveTimer: null,\n      hasUnsavedChanges: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n        console.log('🔍 documentContent 变化:', newVal?.substring(0, 200) + '...')\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时初始化编辑器\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n    this.loadCachedContent()\n\n    // 监听页面关闭事件，保存内容\n    window.addEventListener('beforeunload', this.handleBeforeUnload)\n  },\n\n  beforeDestroy() {\n    // 保存当前内容到缓存\n    this.saveToCacheNow()\n\n    // 清理定时器\n    if (this.autoSaveTimer) {\n      clearTimeout(this.autoSaveTimer)\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('beforeunload', this.handleBeforeUnload)\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n        console.error('获取题目列表失败', error)\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n        console.error('获取统计数据失败', error)\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('批量删除失败', error)\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('批量删除失败', error)\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        this.expandedQuestions.splice(index, 1)\n      } else {\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('删除题目失败', error)\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n      console.log('✅ 文档导入对话框已打开，已清除上次上传内容')\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n        console.log('✅ 输入范例已填充到富文本编辑器')\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n            console.log('✅ 延迟填充输入范例到富文本编辑器')\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n      console.log('✅ 输入范例已填充到编辑器')\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n      console.log('开始上传文件:', file.name, file.type, file.size)\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n      console.log('✅ 文件验证通过，开始上传，显示上传动画')\n      console.log('上传到:', this.uploadUrl)\n      console.log('上传数据:', this.uploadData)\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n      console.log('上传成功回调:', response, file.name)\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n        console.log('✅ 文件上传成功，开始解析，显示解析动画')\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n          console.log('✅ 解析成功:', {\n            questions: response.questions.length,\n            errors: errorCount,\n            errorDetails: response.errors\n          })\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n          console.error('❌ 解析失败:', {\n            errors: response.errors,\n            response: response\n          })\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastSaveTime = new Date().toLocaleString()\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n        console.error('上传失败响应:', response)\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n      console.error('上传失败:', error, file?.name)\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n      console.log(`✅ ${this.allExpanded ? '全部展开' : '全部收起'}题目`)\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 导入成功后清除缓存\n        this.clearCache()\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        console.warn('CKEditor未加载，使用普通文本框')\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          console.error('编辑器容器不存在')\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n            console.error('CKEditor未加载或不可用')\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n                  console.log('CKEditor插件加载完成')\n                },\n                instanceReady: function() {\n                  console.log('CKEditor实例准备就绪')\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      console.log('图像对话框打开')\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              console.log('检测到上传完成:', urlField.getValue())\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                              console.log('已切换到图像信息标签页')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n            console.error('CKEditor完整配置初始化失败，尝试简化配置:', error)\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n                    console.log('CKEditor简化配置实例准备就绪')\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n                        console.log('简化配置：图像对话框打开')\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                console.log('简化配置检测到上传完成:', urlField.getValue())\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                                console.log('简化配置已切换到图像信息标签页')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n              console.log('CKEditor简化配置初始化成功')\n            } catch (fallbackError) {\n              console.error('CKEditor简化配置也失败，使用普通文本框:', fallbackError)\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              console.log('🔍 CKEditor原始内容:', rawContent)\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n              console.log('🔍 转换相对路径后:', contentWithRelativeUrls)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n              console.log('🔍 最终documentContent:', this.documentContent)\n              this.lastSaveTime = new Date().toLocaleString()\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            console.log('CKEditor初始化成功')\n\n            // 优先加载缓存的HTML内容，如果没有则使用documentContent\n            const contentToLoad = this.documentHtmlContent || this.documentContent\n            if (contentToLoad) {\n              console.log('📦 加载内容到编辑器:', contentToLoad.substring(0, 100) + '...')\n              this.richEditor.setData(contentToLoad)\n            }\n          })\n        })\n\n      } catch (error) {\n        console.error('富文本编辑器初始化失败:', error)\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = this.documentContent || ''\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n          this.lastSaveTime = new Date().toLocaleString()\n\n          // 标记有未保存的更改并保存到缓存\n          this.hasUnsavedChanges = true\n          this.saveToCache()\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      console.log('🔍 setEditorContent 输入:', content)\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n        // 更新保存时间\n        this.lastSaveTime = new Date().toLocaleString()\n      } catch (error) {\n        console.error('解析失败', error)\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n        console.warn('⚠️ 解析内容为空或格式不正确')\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n        console.log('开始解析题目内容，长度:', content.length)\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          console.warn('⚠️ 处理后的内容为空')\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n        console.log('总行数:', lines.length)\n\n        if (lines.length === 0) {\n          console.warn('⚠️ 没有有效的内容行')\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n        console.log('前5行内容:', lines.slice(0, 5))\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                console.log(`🔍 解析题目 ${questionNumber}，内容:`, questionText)\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                  console.log(`✅ 解析题目 ${questions.length} 成功:`, {\n                    type: parsedQuestion.questionType,\n                    content: parsedQuestion.questionContent?.substring(0, 50) + '...',\n                    options: parsedQuestion.options?.length || 0,\n                    answer: parsedQuestion.correctAnswer\n                  })\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n                console.error(`❌ 题目 ${questionNumber} 解析失败:`, error)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n            console.log(`🔍 发现题目 ${questionNumber}:`, line)\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n              console.log(`✅ 解析最后题目 ${questions.length}:`, parsedQuestion.questionContent?.substring(0, 50) + '...')\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n            console.error(`❌ 最后题目 ${questionNumber} 解析失败:`, error)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n          console.log('🔍 找到题型标注:', typeText)\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          console.log('🔍 解析出题目类型:', questionType)\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n            console.log('🔍 题型标注同行有内容:', remainingContent)\n          } else {\n            contentStartIndex = i + 1\n            console.log('🔍 题型标注独立一行，从下一行开始解析')\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析，并尝试推断题型\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n        // 尝试根据题目内容推断题型\n        questionType = this.inferQuestionType(lines)\n        console.log('🔍 未找到题型标注，推断题型为:', questionType)\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      console.log(`📝 收集到的题目内容:`, questionContent)\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      console.log(`📝 题目内容解析结果:`, {\n        type: questionType,\n        originalContent: questionContent.trim(),\n        finalContent: finalQuestionContent,\n        hasQuestionNumber: /^\\d+[.:：．、]/.test(finalQuestionContent)\n      })\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n        console.log('🔍 根据选项推断题目类型为:', questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n          console.log('🔍 根据答案长度推断题目类型为:', questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        console.log('🖼️ 处理图片路径，原始内容:', content.substring(0, 100) + '...')\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          console.log('🖼️ 发现图片标签，路径:', src)\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          console.log('🖼️ 转换后的图片标签:', result)\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        console.log('🖼️ stripHtmlTagsKeepImages 输入长度:', content.length)\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          console.log('🖼️ 找到图片标签:', match.substring(0, 50) + '...')\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        console.log('🖼️ stripHtmlTagsKeepImages 完成，输出长度:', finalContent.length)\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n            console.log('🔍 解析到难度:', difficulty)\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题',\n        // 数字格式支持\n        1: '单选题',\n        2: '多选题',\n        3: '判断题',\n        // 字符串数字格式支持\n        '1': '单选题',\n        '2': '多选题',\n        '3': '判断题'\n      }\n\n      // 调试日志\n      console.log('🔍 题型值:', type, '类型:', typeof type)\n\n      // 如果题型为空、null、undefined或未知，返回默认值\n      if (type === null || type === undefined || type === '' || typeMap[type] === undefined) {\n        console.log('🔍 题型为空或未知，使用默认值: 单选题')\n        return '单选题'\n      }\n\n      const result = typeMap[type] || '单选题'\n      console.log('🔍 题型映射结果:', result)\n      return result\n    },\n\n    // 安全获取题型名称（用于题目预览）\n    getQuestionTypeNameSafe(question) {\n      if (!question) {\n        return '单选题'\n      }\n\n      // 首先尝试使用 questionType 字段\n      let questionType = question.questionType\n\n      // 如果 questionType 为空，尝试从其他字段获取\n      if (!questionType && question.type) {\n        questionType = question.type\n      }\n\n      // 如果还是为空，根据题目特征推断\n      if (!questionType) {\n        // 根据选项数量推断\n        if (question.options && question.options.length > 0) {\n          // 根据答案长度判断是单选还是多选\n          if (question.correctAnswer && question.correctAnswer.length > 1 && /^[A-Z]{2,}$/.test(question.correctAnswer)) {\n            questionType = 'multiple'\n          } else {\n            questionType = 'single'\n          }\n        } else {\n          // 没有选项，可能是判断题\n          if (question.correctAnswer && /^(正确|错误|对|错|是|否|true|false|T|F)$/i.test(question.correctAnswer)) {\n            questionType = 'judgment'\n          } else {\n            questionType = 'single' // 默认单选题\n          }\n        }\n      }\n\n      return this.getQuestionTypeName(questionType)\n    },\n\n    // 根据题目内容推断题型\n    inferQuestionType(lines) {\n      if (!lines || lines.length === 0) {\n        return 'single'\n      }\n\n      let hasOptions = false\n      let optionCount = 0\n      let hasJudgmentKeywords = false\n\n      // 检查是否有选项\n      for (const line of lines) {\n        if (this.isOptionLine(line)) {\n          hasOptions = true\n          optionCount++\n        }\n\n        // 检查是否包含判断题关键词\n        if (line.includes('正确') || line.includes('错误') ||\n            line.includes('对') || line.includes('错') ||\n            line.includes('是') || line.includes('否') ||\n            /答案[.:：、]\\s*(正确|错误|对|错|是|否|true|false|T|F)/i.test(line)) {\n          hasJudgmentKeywords = true\n        }\n      }\n\n      // 如果有判断题关键词，推断为判断题\n      if (hasJudgmentKeywords) {\n        return 'judgment'\n      }\n\n      // 如果有选项\n      if (hasOptions) {\n        // 根据选项数量推断\n        if (optionCount >= 4) {\n          return 'single' // 4个或以上选项，通常是单选题\n        } else if (optionCount >= 2) {\n          return 'single' // 2-3个选项，也可能是单选题\n        }\n      }\n\n      // 默认推断为单选题\n      return 'single'\n    },\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n    // ==================== 缓存保存相关方法 ====================\n\n    // 加载缓存的内容\n    loadCachedContent() {\n      try {\n        const cachedData = localStorage.getItem(this.cacheKey)\n        if (cachedData) {\n          const data = JSON.parse(cachedData)\n          console.log('📦 加载缓存内容:', data)\n\n          // 恢复内容\n          this.documentContent = data.documentContent || ''\n          this.documentHtmlContent = data.documentHtmlContent || ''\n          this.lastSaveTime = data.lastSaveTime || ''\n\n          // 如果有内容，标记为有未保存的更改\n          if (this.documentContent || this.documentHtmlContent) {\n            this.hasUnsavedChanges = true\n            console.log('📦 从缓存恢复内容，标记为有未保存更改')\n          }\n        }\n      } catch (error) {\n        console.error('❌ 加载缓存内容失败:', error)\n      }\n    },\n\n    // 保存内容到缓存\n    saveToCache() {\n      // 防抖保存，避免频繁写入\n      if (this.autoSaveTimer) {\n        clearTimeout(this.autoSaveTimer)\n      }\n\n      this.autoSaveTimer = setTimeout(() => {\n        this.saveToCacheNow()\n      }, 2000) // 2秒后保存\n    },\n\n    // 立即保存到缓存\n    saveToCacheNow() {\n      try {\n        const dataToSave = {\n          documentContent: this.documentContent || '',\n          documentHtmlContent: this.documentHtmlContent || '',\n          lastSaveTime: this.lastSaveTime || new Date().toLocaleString(),\n          timestamp: Date.now()\n        }\n\n        localStorage.setItem(this.cacheKey, JSON.stringify(dataToSave))\n        console.log('💾 内容已保存到缓存')\n        this.hasUnsavedChanges = false\n      } catch (error) {\n        console.error('❌ 保存到缓存失败:', error)\n      }\n    },\n\n    // 清除缓存\n    clearCache() {\n      try {\n        localStorage.removeItem(this.cacheKey)\n        this.hasUnsavedChanges = false\n        console.log('🗑️ 缓存已清除')\n      } catch (error) {\n        console.error('❌ 清除缓存失败:', error)\n      }\n    },\n\n    // 页面关闭前的处理\n    handleBeforeUnload(event) {\n      if (this.hasUnsavedChanges) {\n        // 立即保存到缓存\n        this.saveToCacheNow()\n\n        // 提示用户有未保存的更改\n        const message = '您有未保存的更改，确定要离开吗？'\n        event.returnValue = message\n        return message\n      }\n    },\n\n    // 手动保存\n    manualSave() {\n      this.saveToCacheNow()\n      this.$message.success('内容已保存到本地缓存')\n    },\n\n    // 处理缓存相关命令\n    handleCacheCommand(command) {\n      switch (command) {\n        case 'clear':\n          this.$confirm('确定要清除所有缓存的草稿内容吗？', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.clearCache()\n            this.documentContent = ''\n            this.documentHtmlContent = ''\n            this.lastSaveTime = ''\n\n            // 清空编辑器内容\n            if (this.richEditor && this.editorInitialized) {\n              this.richEditor.setData('')\n            }\n\n            this.$message.success('缓存已清除')\n          }).catch(() => {\n            // 用户取消\n          })\n          break\n        case 'export':\n          this.exportDraft()\n          break\n      }\n    },\n\n    // 导出草稿\n    exportDraft() {\n      if (!this.documentHtmlContent && !this.documentContent) {\n        this.$message.warning('没有可导出的草稿内容')\n        return\n      }\n\n      const content = this.documentHtmlContent || this.documentContent\n      const blob = new Blob([content], { type: 'text/html;charset=utf-8' })\n      const url = URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `题目草稿_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      URL.revokeObjectURL(url)\n\n      this.$message.success('草稿已导出')\n    },\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理多余的空行和空白字符\n      content = this.cleanupQuestionContent(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的多余空行和空白字符\n    cleanupQuestionContent(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        return content\n          // 移除空的段落标签\n          .replace(/<p[^>]*>\\s*<\\/p>/gi, '')\n          // 移除段落间多余的空白\n          .replace(/(<\\/p>)\\s*(<p[^>]*>)/gi, '$1$2')\n          // 移除开头和结尾的空白\n          .trim()\n      } else {\n        // 处理纯文本内容\n        return content\n          // 移除多余的空行（保留单个换行）\n          .replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n')\n          // 移除行首行尾空白\n          .replace(/^\\s+|\\s+$/gm, '')\n          // 移除开头和结尾的空白\n          .trim()\n      }\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干和答案同一行显示 */\n.question-main-line {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  flex: 1;\n  margin: 0;\n}\n\n.question-answer-inline {\n  flex-shrink: 0;\n  color: #409eff;\n  font-weight: 500;\n  background: #f0f9ff;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 13px;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n/* 题目内容区域样式 */\n.question-main-content {\n  margin-bottom: 12px;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n/* 题目内容中的段落样式 */\n.question-main-content p {\n  margin: 0 0 8px 0;\n  line-height: 1.6;\n}\n\n.question-main-content p:last-child {\n  margin-bottom: 0;\n}\n\n/* 题目选项样式 */\n.question-options {\n  margin: 8px 0;\n}\n\n.option-item {\n  margin: 4px 0;\n  line-height: 1.5;\n  font-size: 14px;\n}\n\n/* 答案区域样式 */\n.question-answer-section {\n  margin-top: 12px;\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.question-answer-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.question-answer-value {\n  color: #333;\n  font-weight: 500;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4hBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AAAA,IAAAK,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,uBAEA,sBACA,6BACA,4BAEA,+BACA,kCACA,8BAEA,8BACA,6BACA,6BAEA,SAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,yBACA,wBACA,oBACA,oBAEA,+BAEA,wBACA,iBAEA,gDACA,4BACA,uCACA,YAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,wBACA,yBAEA,4BAEA,qBACA,yBACA;MACAmB,OAAA;MACAC,cAAA;IACA,iBAEAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,yDACA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA,kBACA,mBAEA,4BACA;EAEA;EAEAC,KAAA;IACA;IACAC,eAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAAC,oBAAA;UACA;QACA;QAEAC,OAAA,CAAAC,GAAA,4BAAAH,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAI,SAAA;QAEA,IAAAJ,MAAA,IAAAA,MAAA,CAAAK,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;IACAC,mBAAA;MACAX,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAW,KAAA;QACA,IAAAX,MAAA;UACA;UACA,KAAAY,SAAA;YACAD,KAAA,CAAAE,cAAA;UACA;QACA;UACA;UACA,SAAAC,UAAA;YACA,KAAAA,UAAA,CAAAC,OAAA;YACA,KAAAD,UAAA;YACA,KAAAE,iBAAA;UACA;QACA;MACA;MACAP,SAAA;IACA;EACA;EAEAQ,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAZ,qBAAA,QAAAa,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,UAAA;MACAnD,MAAA,OAAAA;IACA;IACA,KAAAoD,aAAA;MACA7B,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEA2B,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,iBAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,sBAAAC,kBAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAC,cAAA;;IAEA;IACA,SAAAC,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;;IAEA;IACAL,MAAA,CAAAO,mBAAA,sBAAAL,kBAAA;;IAEA;IACA,SAAAb,UAAA;MACA,KAAAA,UAAA,CAAAC,OAAA;MACA,KAAAD,UAAA;IACA;EACA;EACAmB,OAAA,GAAAvE,QAAA;IACA;IACAwD,QAAA,WAAAA,SAAA;MACA,IAAAgB,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAAlE,MAAA,GAAAgE,kBAAA,CAAAhE,MAAA;QAAAC,QAAA,GAAA+D,kBAAA,CAAA/D,QAAA;MACA,KAAAD,MAAA;QACA,KAAAmE,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAArE,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAAsE,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAApE,WAAA;MACA,IAAAqE,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAAnE,YAAA,GAAAwE,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAAvE,KAAA,GAAA4E,QAAA,CAAA5E,KAAA;MACA,GAAA8E,KAAA,WAAAb,KAAA;QACApC,OAAA,CAAAoC,KAAA,aAAAA,KAAA;QACAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAAlE,OAAA,MAAA0D,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAAvE,YAAA;QACA,IAAAyE,OAAA;UACA;UACA;UACA;QACA;QACAF,eAAA,CAAAvE,YAAA,GAAAyE,OAAA,CAAAF,eAAA,CAAAvE,YAAA,KAAAuE,eAAA,CAAAvE,YAAA;MACA;;MAEA;MACA,IAAAuE,eAAA,CAAAtE,UAAA;QACA,IAAAyE,aAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAAtE,UAAA,GAAAyE,aAAA,CAAAH,eAAA,CAAAtE,UAAA,KAAAsE,eAAA,CAAAtE,UAAA;MACA;;MAEA;MACA0E,MAAA,CAAAC,IAAA,CAAAL,eAAA,EAAAM,OAAA,WAAAC,GAAA;QACA,IAAAP,eAAA,CAAAO,GAAA,YAAAP,eAAA,CAAAO,GAAA,cAAAP,eAAA,CAAAO,GAAA,MAAAC,SAAA;UACA,OAAAR,eAAA,CAAAO,GAAA;QACA;MACA;MAEA,OAAAP,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,+BAAA,OAAA5F,MAAA,EAAA8E,IAAA,WAAAC,QAAA;QACAY,MAAA,CAAAzF,UAAA,GAAA6E,QAAA,CAAAjF,IAAA;MACA,GAAAmF,KAAA,WAAAb,KAAA;QACApC,OAAA,CAAAoC,KAAA,aAAAA,KAAA;QACA;QACAuB,MAAA,CAAAzF,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IACA;IACAuF,iBAAA,WAAAA,kBAAA;MACA,KAAAC,kBAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAC,mBAAA,GAAAD,IAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,mBAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAtF,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAuF,iBAAA;MACA;IACA;IAIA;IACAC,qBAAA,WAAAA,sBAAA;MACA,SAAAvF,iBAAA,CAAAwF,MAAA;QACA,KAAApC,QAAA,CAAAqC,OAAA;QACA;MACA;MACA,KAAArC,QAAA,CAAAsC,IAAA,6BAAAC,MAAA,MAAA3F,iBAAA,CAAAwF,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAA7F,iBAAA,QAAAR,YAAA,CAAAsG,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAA5C,QAAA,CAAA6C,OAAA,uBAAAN,MAAA,MAAA3F,iBAAA,CAAAwF,MAAA;MACA;QACA;QACA,KAAAxF,iBAAA;QACA,KAAAoD,QAAA,CAAA6C,OAAA;MACA;IACA;IAIA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAnG,iBAAA,CAAAwF,MAAA;QACA,KAAApC,QAAA,CAAAqC,OAAA;QACA;MACA;MAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAA3F,iBAAA,CAAAwF,MAAA;QACAa,iBAAA;QACAC,gBAAA;QACArB,IAAA;MACA,GAAAlB,IAAA;QACA;QACA;QACA,IAAAwC,cAAA,GAAAJ,MAAA,CAAAnG,iBAAA,CAAA8F,GAAA,WAAAE,UAAA;UAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;QAAA,CACA;QAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAxC,IAAA;UACAoC,MAAA,CAAA/C,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAQ,MAAA,CAAAnG,iBAAA,CAAAwF,MAAA;UACAW,MAAA,CAAAnG,iBAAA;UACAmG,MAAA,CAAAQ,WAAA;UACAR,MAAA,CAAA5C,eAAA;UACA4C,MAAA,CAAA3C,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UACApC,OAAA,CAAAoC,KAAA,WAAAA,KAAA;UACA8C,MAAA,CAAA/C,QAAA,CAAAC,KAAA;QACA;MACA;IACA;EAAA,OAAApD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,gCAAAyH,kBAAA,EAGA;IAAA,IAAAU,MAAA;IACA,SAAA5G,iBAAA,CAAAwF,MAAA;MACA,KAAApC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAA3F,iBAAA,CAAAwF,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACA;MACA,IAAAwC,cAAA,GAAAK,MAAA,CAAA5G,iBAAA,CAAA8F,GAAA,WAAAE,UAAA;QAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;MAAA,CACA;MAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAxC,IAAA;QACA6C,MAAA,CAAAxD,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAiB,MAAA,CAAA5G,iBAAA,CAAAwF,MAAA;QACAoB,MAAA,CAAA5G,iBAAA;QACA4G,MAAA,CAAAf,aAAA;QACAe,MAAA,CAAArD,eAAA;QACAqD,MAAA,CAAApD,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QACApC,OAAA,CAAAoC,KAAA,WAAAA,KAAA;QACAuD,MAAA,CAAAxD,QAAA,CAAAC,KAAA;MACA;IACA,GAAAa,KAAA;MACA0C,MAAA,CAAAxD,QAAA,CAAAsC,IAAA;IACA;EACA,qCAGAmB,qBAAAb,UAAA,EAAAc,QAAA;IACA,IAAAA,QAAA;MACA,UAAA9G,iBAAA,CAAA+G,QAAA,CAAAf,UAAA;QACA,KAAAhG,iBAAA,CAAAgH,IAAA,CAAAhB,UAAA;MACA;IACA;MACA,IAAAiB,KAAA,QAAAjH,iBAAA,CAAAkH,OAAA,CAAAlB,UAAA;MACA,IAAAiB,KAAA;QACA,KAAAjH,iBAAA,CAAAmH,MAAA,CAAAF,KAAA;MACA;IACA;;IAEA;IACA,KAAApB,aAAA,QAAA7F,iBAAA,CAAAwF,MAAA,UAAAhG,YAAA,CAAAgG,MAAA;EACA,mCAEA4B,mBAAApB,UAAA;IACA,IAAAiB,KAAA,QAAA3B,iBAAA,CAAA4B,OAAA,CAAAlB,UAAA;IACA,IAAAiB,KAAA;MACA,KAAA3B,iBAAA,CAAA6B,MAAA,CAAAF,KAAA;IACA;MACA,KAAA3B,iBAAA,CAAA0B,IAAA,CAAAhB,UAAA;IACA;EACA,mCAEAqB,mBAAAC,QAAA;IACA,KAAAnC,mBAAA,GAAAmC,QAAA;IACA,KAAApC,mBAAA,GAAAoC,QAAA,CAAA1H,YAAA;IACA,KAAAwF,mBAAA;EACA,mCAEAmC,mBAAAD,QAAA;IACA;IACA,IAAAE,cAAA,OAAApD,cAAA,CAAAlE,OAAA,MAAAkE,cAAA,CAAAlE,OAAA,MACAoH,QAAA;MACAtB,UAAA;MAAA;MACAyB,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;IAAA,EACA;;IAEA;IACA,KAAAzC,mBAAA,GAAAqC,cAAA;IACA,KAAAtC,mBAAA,QAAA2C,2BAAA,CAAAP,QAAA,CAAA1H,YAAA;IACA,KAAAwF,mBAAA;EACA,4CAGAyC,4BAAA5C,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA,KAAAA,IAAA;EACA,qCAEA6C,qBAAAR,QAAA;IAAA,IAAAS,MAAA;IACA,IAAAjI,eAAA,GAAAwH,QAAA,CAAAxH,eAAA,CAAAkI,OAAA;IACA,IAAAC,cAAA,GAAAnI,eAAA,CAAA0F,MAAA,QAAA1F,eAAA,CAAAqB,SAAA,kBAAArB,eAAA;IACA,KAAAsG,QAAA,0CAAAT,MAAA,CAAAsC,cAAA;MACA5B,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACA,IAAAyC,qBAAA,EAAAc,QAAA,CAAAtB,UAAA,EAAAjC,IAAA;QACAgE,MAAA,CAAA3E,QAAA,CAAA6C,OAAA;QACA8B,MAAA,CAAAxE,eAAA;QACAwE,MAAA,CAAAvE,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QACApC,OAAA,CAAAoC,KAAA,WAAAA,KAAA;QACA0E,MAAA,CAAA3E,QAAA,CAAAC,KAAA;MACA;IACA;EACA,0CAEA6E,0BAAA;IACA,KAAA9C,mBAAA;IACA,KAAA7B,eAAA;IACA,KAAAC,aAAA;EACA,yCAEA2E,yBAAA;IACA,KAAApD,kBAAA;IACA,KAAAtD,mBAAA;IACA,KAAA8B,eAAA;IACA,KAAAC,aAAA;EACA,kCAKA4E,kBAAAC,IAAA;IACAA,IAAA;EACA,QAAApI,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,uCAGA6J,yBAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;;IAEA;IACA,KAAA9G,SAAA;MACA,IAAA+G,eAAA,GAAAH,MAAA,CAAAI,KAAA,CAAAC,cAAA;MACA,IAAAF,eAAA;QACAA,eAAA,CAAAG,UAAA;MACA;IACA;IAEA,KAAAC,2BAAA;IACA7H,OAAA,CAAAC,GAAA;EACA,gCAGA6H,gBAAA;IACA,KAAAC,aAAA;IACA,KAAAC,kBAAA;EACA,oCAGAC,oBAAA;IAAA,IAAAC,MAAA;IACA;IACA,IAAAC,YAAA,muDAuBAhI,IAAA;;IAEA;IACA,SAAAS,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAwH,OAAA,CAAAD,YAAA;MACAnI,OAAA,CAAAC,GAAA;IACA;MACA;MACA,KAAAS,SAAA;QACA,IAAAwH,MAAA,CAAAtH,UAAA,IAAAsH,MAAA,CAAApH,iBAAA;UACAoH,MAAA,CAAAtH,UAAA,CAAAwH,OAAA,CAAAD,YAAA;UACAnI,OAAA,CAAAC,GAAA;QACA;MACA;IACA;;IAEA;IACA,KAAA+H,kBAAA;;IAEA;IACA,KAAA7F,QAAA,CAAA6C,OAAA;IAEAhF,OAAA,CAAAC,GAAA;EACA,sCAGAoI,sBAAA;IACA,KAAAC,QAAA;EACA,qCAGAC,qBAAA;IACA,KAAAD,QAAA;EACA,6BAGAE,aAAAC,IAAA;IACAzI,OAAA,CAAAC,GAAA,YAAAwI,IAAA,CAAAhL,IAAA,EAAAgL,IAAA,CAAAzE,IAAA,EAAAyE,IAAA,CAAAC,IAAA;IAEA,IAAAC,WAAA,GAAAF,IAAA,CAAAzE,IAAA,kFACAyE,IAAA,CAAAzE,IAAA,4EACAyE,IAAA,CAAAhL,IAAA,CAAAmL,QAAA,aAAAH,IAAA,CAAAhL,IAAA,CAAAmL,QAAA;IACA,IAAAC,OAAA,GAAAJ,IAAA,CAAAC,IAAA;IAEA,KAAAC,WAAA;MACA,KAAAxG,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAAyG,OAAA;MACA,KAAA1G,QAAA,CAAAC,KAAA;MACA;IACA;;IAEA;IACA,KAAAjB,UAAA,CAAAnD,MAAA,QAAAA,MAAA;;IAEA;IACA,KAAAuJ,WAAA;IACA,KAAAC,SAAA;IAEAxH,OAAA,CAAAC,GAAA;IACAD,OAAA,CAAAC,GAAA,cAAA6I,SAAA;IACA9I,OAAA,CAAAC,GAAA,eAAAkB,UAAA;IAEA;EACA,oCAGA4H,oBAAAhG,QAAA,EAAA0F,IAAA;IAAA,IAAAO,MAAA;IACAhJ,OAAA,CAAAC,GAAA,YAAA8C,QAAA,EAAA0F,IAAA,CAAAhL,IAAA;IAEA,IAAAsF,QAAA,CAAAkG,IAAA;MACA;MACA,KAAA1B,WAAA;MACA,KAAAC,SAAA;MAEAxH,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAAI,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA4I,UAAA;QACAF,MAAA,CAAAnB,2BAAA;QACAmB,MAAA,CAAAxB,SAAA;MACA;;MAEA;MACA,KAAAzH,oBAAA;;MAEA;MACA,IAAAgD,QAAA,CAAAoG,SAAA,IAAApG,QAAA,CAAAoG,SAAA,CAAA5E,MAAA;QACA,KAAAlE,eAAA,GAAA0C,QAAA,CAAAoG,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;UAAA,WAAAlD,cAAA,CAAAlE,OAAA,MAAAkE,cAAA,CAAAlE,OAAA,MACAoH,QAAA;YACA+C,SAAA;UAAA;QAAA,CACA;QACA;QACA,KAAAC,WAAA;QACA,KAAA/I,WAAA,GAAAyC,QAAA,CAAAuG,MAAA;;QAEA;QACA,IAAAC,UAAA,GAAAxG,QAAA,CAAAuG,MAAA,GAAAvG,QAAA,CAAAuG,MAAA,CAAA/E,MAAA;QACA,IAAAgF,UAAA;UACA,KAAApH,QAAA,CAAA6C,OAAA,mCAAAN,MAAA,CAAA3B,QAAA,CAAAoG,SAAA,CAAA5E,MAAA,sCAAAG,MAAA,CAAA6E,UAAA;QACA;UACA,KAAApH,QAAA,CAAA6C,OAAA,mCAAAN,MAAA,CAAA3B,QAAA,CAAAoG,SAAA,CAAA5E,MAAA;QACA;QAEAvE,OAAA,CAAAC,GAAA;UACAkJ,SAAA,EAAApG,QAAA,CAAAoG,SAAA,CAAA5E,MAAA;UACA+E,MAAA,EAAAC,UAAA;UACAC,YAAA,EAAAzG,QAAA,CAAAuG;QACA;MACA;QACA,KAAAnH,QAAA,CAAAC,KAAA;QACA,KAAA/B,eAAA;QACA,KAAAC,WAAA,GAAAyC,QAAA,CAAAuG,MAAA;QAEAtJ,OAAA,CAAAoC,KAAA;UACAkH,MAAA,EAAAvG,QAAA,CAAAuG,MAAA;UACAvG,QAAA,EAAAA;QACA;MACA;;MAEA;MACA,IAAAA,QAAA,CAAA0G,eAAA;QACA,KAAAC,gBAAA,CAAA3G,QAAA,CAAA0G,eAAA;QACA,KAAA7J,eAAA,GAAAmD,QAAA,CAAA0G,eAAA;QACA,KAAAE,mBAAA,GAAA5G,QAAA,CAAA0G,eAAA;QACA,KAAAG,YAAA,OAAAC,IAAA,GAAAC,cAAA;MACA;;MAEA;MACAZ,UAAA;QACAF,MAAA,CAAAjJ,oBAAA;MACA;IACA;MACAC,OAAA,CAAAoC,KAAA,YAAAW,QAAA;MACA,KAAAZ,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAAgH,GAAA;MACA;MACA,KAAAxC,WAAA;MACA,KAAAC,SAAA;IACA;EACA,kCAGAwC,kBAAA5H,KAAA,EAAAqG,IAAA;IACAzI,OAAA,CAAAoC,KAAA,UAAAA,KAAA,EAAAqG,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAhL,IAAA;IACA,KAAA0E,QAAA,CAAAC,KAAA;;IAEA;IACA,KAAAmF,WAAA;IACA,KAAAC,SAAA;EACA,4BAGAyC,YAAA;IAAA,IAAAC,MAAA;IACA,KAAA7J,eAAA,CAAAmD,OAAA,WAAA6C,QAAA;MACA6D,MAAA,CAAAC,IAAA,CAAA9D,QAAA;IACA;EACA,+BAGA+D,eAAApE,KAAA;IACA,IAAAK,QAAA,QAAAhG,eAAA,CAAA2F,KAAA;IACA,KAAAmE,IAAA,CAAA9D,QAAA,gBAAAA,QAAA,CAAA+C,SAAA;EACA,QAAApK,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,iCAGA6M,mBAAA;IAAA,IAAAC,MAAA;IACA,KAAAjB,WAAA,SAAAA,WAAA;IACA,KAAAhJ,eAAA,CAAAmD,OAAA,WAAA6C,QAAA;MACAiE,MAAA,CAAAH,IAAA,CAAA9D,QAAA,gBAAAiE,MAAA,CAAAjB,WAAA;IACA;IACArJ,OAAA,CAAAC,GAAA,WAAAyE,MAAA,MAAA2E,WAAA;EACA,8BAGAkB,cAAA;IAAA,IAAAC,OAAA;IACA,SAAAnK,eAAA,CAAAkE,MAAA;MACA,KAAApC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,6BAAAT,MAAA,MAAArE,eAAA,CAAAkE,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACA0H,OAAA,CAAAC,eAAA;IACA,GAAAxH,KAAA;EACA,gCAGAwH,gBAAA;IAAA,IAAAC,OAAA;IAAA,WAAAC,kBAAA,CAAA1L,OAAA,mBAAA2L,aAAA,CAAA3L,OAAA,IAAA4L,CAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAAjI,QAAA,EAAAkI,EAAA;MAAA,WAAAL,aAAA,CAAA3L,OAAA,IAAAiM,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAEA;YACAN,iBAAA,OAAAO,mBAAA,CAAArM,OAAA,EAAAyL,OAAA,CAAArK,eAAA;YAEA,IAAAqK,OAAA,CAAAa,aAAA,CAAArM,OAAA;cACA6L,iBAAA,CAAA7L,OAAA;YACA;;YAEA;YACA8L,UAAA;cACAhN,MAAA,EAAA0M,OAAA,CAAA1M,MAAA;cACAmL,SAAA,EAAA4B,iBAAA;cACA5L,cAAA,EAAAuL,OAAA,CAAAa,aAAA,CAAApM;YACA;YAAAgM,QAAA,CAAAC,CAAA;YAAA,OAEA,IAAAI,kCAAA,EAAAR,UAAA;UAAA;YAAAjI,QAAA,GAAAoI,QAAA,CAAAM,CAAA;YAAA,MAEA1I,QAAA,CAAAkG,IAAA;cAAAkC,QAAA,CAAAC,CAAA;cAAA;YAAA;YACAV,OAAA,CAAAvI,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAqG,iBAAA,CAAAxG,MAAA;YAAA4G,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAA,MAEA,IAAAM,KAAA,CAAA3I,QAAA,CAAAgH,GAAA;UAAA;YAEAW,OAAA,CAAAlK,mBAAA;YACAkK,OAAA,CAAA9K,eAAA;YACA8K,OAAA,CAAAf,mBAAA;YACAe,OAAA,CAAArK,eAAA;YACAqK,OAAA,CAAApK,WAAA;;YAEA;YACAoK,OAAA,CAAAiB,UAAA;YAEAjB,OAAA,CAAApI,eAAA;YACAoI,OAAA,CAAAnI,aAAA;YAAA4I,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAAAJ,EAAA,GAAAE,QAAA,CAAAM,CAAA;YAEAzL,OAAA,CAAAoC,KAAA,SAAA6I,EAAA;YACAP,OAAA,CAAAvI,QAAA,CAAAC,KAAA;UAAA;YAAA,OAAA+I,QAAA,CAAAS,CAAA;QAAA;MAAA,GAAAd,OAAA;IAAA;EAEA,+BAGAnK,eAAA;IAAA,IAAAkL,OAAA;IACA,SAAA/K,iBAAA;MACA;IACA;;IAEA;IACA,KAAAS,MAAA,CAAAuK,QAAA;MACA9L,OAAA,CAAA+L,IAAA;MACA,KAAAC,kBAAA;MACA;IACA;IAEA;MACA;MACA,SAAApL,UAAA;QACA,KAAAA,UAAA,CAAAC,OAAA;QACA,KAAAD,UAAA;MACA;;MAEA;MACA,IAAAqL,eAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,eAAA;QACAjM,OAAA,CAAAoC,KAAA;QACA;MACA;;MAEA;MACA6J,eAAA,CAAAG,SAAA;;MAEA;MACA,KAAA1L,SAAA;QACA;QACA,KAAAa,MAAA,CAAAuK,QAAA,KAAAvK,MAAA,CAAAuK,QAAA,CAAA/E,OAAA;UACA/G,OAAA,CAAAoC,KAAA;UACAyJ,OAAA,CAAAQ,kBAAA;UACA;QACA;QAEA;UACA;UACAR,OAAA,CAAAjL,UAAA,GAAAW,MAAA,CAAAuK,QAAA,CAAA/E,OAAA,6BAAA/H,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;YACAqN,MAAA;YAAA;YACAC,OAAA,GACA;cAAA9O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,GACA;cAAA/O,IAAA;cAAA+O,KAAA;YAAA,EACA;YACAC,aAAA;YACAC,QAAA;YACAC,aAAA;YACAC,cAAA;YACAC,YAAA;YACAC,cAAA;YACA;YACAC,cAAA;YACAC,qBAAA;YACA;YACAC,sBAAA;YACAC,kBAAA;YACA;YACAC,oBAAA,EAAA/N,OAAA,CAAAC,GAAA,CAAAC,gBAAA;YACA8N,iBAAA;YACA;YACAC,QAAA;UAAA,wBAEA,uCACA,2BAEA,oCAEA;YACAC,aAAA,WAAAA,cAAA;cACAtN,OAAA,CAAAC,GAAA;YACA;YACAsN,aAAA,WAAAA,cAAA;cACAvN,OAAA,CAAAC,GAAA;cAEA,IAAAuN,MAAA,GAAAC,GAAA,CAAAD,MAAA;;cAEA;cACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;gBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA3P,IAAA;gBACA,IAAA6P,MAAA,CAAAC,OAAA;kBACA5N,OAAA,CAAAC,GAAA;;kBAEA;kBACAiJ,UAAA;oBACA,IAAA2E,aAAA,GAAAC,WAAA;sBACA;wBACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;wBACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;0BACAlO,OAAA,CAAAC,GAAA,aAAA8N,QAAA,CAAAE,QAAA;0BACAE,aAAA,CAAAN,aAAA;;0BAEA;0BACAF,MAAA,CAAAS,UAAA;0BACApO,OAAA,CAAAC,GAAA;wBACA;sBACA,SAAAoO,CAAA;wBACA;sBAAA;oBAEA;;oBAEA;oBACAnF,UAAA;sBAAA,OAAAiF,aAAA,CAAAN,aAAA;oBAAA;kBACA;gBACA;cACA;YACA;UAEA,EACA;QACA,SAAAzL,KAAA;UACApC,OAAA,CAAAoC,KAAA,8BAAAA,KAAA;;UAEA;UACA;YACAyJ,OAAA,CAAAjL,UAAA,GAAAW,MAAA,CAAAuK,QAAA,CAAA/E,OAAA;cACAuF,MAAA;cACAC,OAAA,GACA,2CACA,kCACA,uBACA,kBACA,oBACA,sCACA;cACAE,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAK,oBAAA,EAAA/N,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACA8N,iBAAA;cACA;cACAC,QAAA;cACA;cACAiB,gBAAA;cACA;cACAZ,EAAA;gBACAH,aAAA,WAAAA,cAAAE,GAAA;kBACAzN,OAAA,CAAAC,GAAA;kBAEA,IAAAuN,MAAA,GAAAC,GAAA,CAAAD,MAAA;;kBAEA;kBACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;oBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA3P,IAAA;oBACA,IAAA6P,MAAA,CAAAC,OAAA;sBACA5N,OAAA,CAAAC,GAAA;;sBAEA;sBACAiJ,UAAA;wBACA,IAAA2E,aAAA,GAAAC,WAAA;0BACA;4BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;4BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;8BACAlO,OAAA,CAAAC,GAAA,iBAAA8N,QAAA,CAAAE,QAAA;8BACAE,aAAA,CAAAN,aAAA;;8BAEA;8BACAF,MAAA,CAAAS,UAAA;8BACApO,OAAA,CAAAC,GAAA;4BACA;0BACA,SAAAoO,CAAA;4BACA;0BAAA;wBAEA;;wBAEA;wBACAnF,UAAA;0BAAA,OAAAiF,aAAA,CAAAN,aAAA;wBAAA;sBACA;oBACA;kBACA;gBAGA;cACA;YACA;YACA7N,OAAA,CAAAC,GAAA;UACA,SAAAsO,aAAA;YACAvO,OAAA,CAAAoC,KAAA,6BAAAmM,aAAA;YACA1C,OAAA,CAAAQ,kBAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAR,OAAA,CAAAjL,UAAA,IAAAiL,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;UACA7B,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;YACA,IAAAc,UAAA,GAAA3C,OAAA,CAAAjL,UAAA,CAAA6N,OAAA;YACAzO,OAAA,CAAAC,GAAA,qBAAAuO,UAAA;YACA,IAAAE,uBAAA,GAAA7C,OAAA,CAAA8C,qBAAA,CAAAH,UAAA;YACAxO,OAAA,CAAAC,GAAA,gBAAAyO,uBAAA;;YAEA;YACA7C,OAAA,CAAAlC,mBAAA,GAAAkC,OAAA,CAAA+C,0BAAA,CAAAF,uBAAA;YACA;YACA7C,OAAA,CAAAjM,eAAA,GAAAiM,OAAA,CAAAgD,uBAAA,CAAAH,uBAAA;YACA1O,OAAA,CAAAC,GAAA,0BAAA4L,OAAA,CAAAjM,eAAA;YACAiM,OAAA,CAAAjC,YAAA,OAAAC,IAAA,GAAAC,cAAA;;YAEA;YACA+B,OAAA,CAAAiD,iBAAA;YACAjD,OAAA,CAAAkD,WAAA;UACA;QACA;;QAEA;QACAlD,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;UACAxE,UAAA;YACA,IAAAsF,UAAA,GAAA3C,OAAA,CAAAjL,UAAA,CAAA6N,OAAA;YACA,IAAAC,uBAAA,GAAA7C,OAAA,CAAA8C,qBAAA,CAAAH,UAAA;;YAEA;YACA3C,OAAA,CAAAlC,mBAAA,GAAAkC,OAAA,CAAA+C,0BAAA,CAAAF,uBAAA;YACA;YACA7C,OAAA,CAAAjM,eAAA,GAAAiM,OAAA,CAAAgD,uBAAA,CAAAH,uBAAA;;YAEA;YACA7C,OAAA,CAAAiD,iBAAA;YACAjD,OAAA,CAAAkD,WAAA;UACA;QACA;;QAEA;QACAlD,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;UACA7B,OAAA,CAAA/K,iBAAA;UACAd,OAAA,CAAAC,GAAA;;UAEA;UACA,IAAA+O,aAAA,GAAAnD,OAAA,CAAAlC,mBAAA,IAAAkC,OAAA,CAAAjM,eAAA;UACA,IAAAoP,aAAA;YACAhP,OAAA,CAAAC,GAAA,iBAAA+O,aAAA,CAAA9O,SAAA;YACA2L,OAAA,CAAAjL,UAAA,CAAAwH,OAAA,CAAA4G,aAAA;UACA;QACA;MACA;IAEA,SAAA5M,KAAA;MACApC,OAAA,CAAAoC,KAAA,iBAAAA,KAAA;MACA;MACA,KAAA4J,kBAAA;IACA;EACA,mCAGAA,mBAAA;IAAA,IAAAiD,OAAA;IACA,IAAAhD,eAAA,GAAAC,QAAA,CAAAC,cAAA;IACA,IAAAF,eAAA;MACA,IAAAiD,QAAA,GAAAhD,QAAA,CAAAiD,aAAA;MACAD,QAAA,CAAAE,SAAA;MACAF,QAAA,CAAAG,WAAA;MACAH,QAAA,CAAAI,KAAA,QAAA1P,eAAA;MACAsP,QAAA,CAAAK,KAAA,CAAAC,OAAA;;MAEA;MACAN,QAAA,CAAA1N,gBAAA,oBAAA6M,CAAA;QACAY,OAAA,CAAArP,eAAA,GAAAyO,CAAA,CAAAoB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAAtF,mBAAA,GAAA0E,CAAA,CAAAoB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAArF,YAAA,OAAAC,IAAA,GAAAC,cAAA;;QAEA;QACAmF,OAAA,CAAAH,iBAAA;QACAG,OAAA,CAAAF,WAAA;MACA;MAEA9C,eAAA,CAAAG,SAAA;MACAH,eAAA,CAAAyD,WAAA,CAAAR,QAAA;MACA,KAAApO,iBAAA;IACA;EACA,8BAGA6O,cAAAC,IAAA;IACA,IAAAC,GAAA,GAAA3D,QAAA,CAAAiD,aAAA;IACAU,GAAA,CAAAzD,SAAA,GAAAwD,IAAA;IACA,OAAAC,GAAA,CAAAC,WAAA,IAAAD,GAAA,CAAAE,SAAA;EACA,iCAGArG,iBAAAsG,OAAA;IACAhQ,OAAA,CAAAC,GAAA,4BAAA+P,OAAA;IACA,SAAApP,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAwH,OAAA,CAAA4H,OAAA;IACA;MACA;MACA,KAAApQ,eAAA,GAAAoQ,OAAA;MACA,KAAArG,mBAAA,GAAAqG,OAAA;IACA;EACA,yBAKA/O,SAAAgP,IAAA,EAAAC,IAAA;IACA,IAAAC,OAAA;IACA,gBAAAC,iBAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAA/L,MAAA,EAAAgM,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACA,IAAAC,KAAA,YAAAA,MAAA;QACA7O,YAAA,CAAAsO,OAAA;QACAF,IAAA,CAAAU,KAAA,SAAAJ,IAAA;MACA;MACA1O,YAAA,CAAAsO,OAAA;MACAA,OAAA,GAAAjH,UAAA,CAAAwH,KAAA,EAAAR,IAAA;IACA;EACA,sCAGAvB,sBAAAqB,OAAA;IACA,KAAAA,OAAA,SAAAA,OAAA;;IAEA;IACA,IAAAY,aAAA,GAAArP,MAAA,CAAAsP,QAAA,CAAAC,MAAA;IACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAA7J,OAAA;IAEA,OAAAiJ,OAAA,CAAAjJ,OAAA,CAAAgK,QAAA;EACA,8BAGA7P,cAAA;IACA,UAAAtB,eAAA,CAAAO,IAAA;MACA,KAAAE,eAAA;MACA,KAAAC,WAAA;MACA;IACA;IAEA;MACA,IAAA2Q,WAAA,QAAAC,oBAAA,MAAAtR,eAAA;MACA;MACA,KAAAS,eAAA,GAAA4Q,WAAA,CAAA9H,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;QAAA,WAAAlD,cAAA,CAAAlE,OAAA,MAAAkE,cAAA,CAAAlE,OAAA,MACAoH,QAAA;UACA+C,SAAA;QAAA;MAAA,CACA;MACA,KAAA9I,WAAA,GAAA2Q,WAAA,CAAA3H,MAAA;;MAEA;MACA,KAAAM,YAAA,OAAAC,IAAA,GAAAC,cAAA;IACA,SAAA1H,KAAA;MACApC,OAAA,CAAAoC,KAAA,SAAAA,KAAA;MACA,KAAA9B,WAAA,cAAA8B,KAAA,CAAA+O,OAAA;MACA,KAAA9Q,eAAA;IACA;EACA,QAAArB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,mCAGA0T,qBAAAlB,OAAA;IACA,IAAA7G,SAAA;IACA,IAAAG,MAAA;IAEA,KAAA0G,OAAA,WAAAA,OAAA;MACAhQ,OAAA,CAAA+L,IAAA;MACA;QAAA5C,SAAA,EAAAA,SAAA;QAAAG,MAAA;MAAA;IACA;IAEA;MACAtJ,OAAA,CAAAC,GAAA,iBAAA+P,OAAA,CAAAzL,MAAA;;MAEA;MACA,IAAAuL,WAAA,QAAAjB,uBAAA,CAAAmB,OAAA;MAEA,KAAAF,WAAA,IAAAA,WAAA,CAAA3P,IAAA,GAAAoE,MAAA;QACAvE,OAAA,CAAA+L,IAAA;QACA;UAAA5C,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;;MAEA;MACA,IAAA8H,KAAA,GAAAtB,WAAA,CAAAuB,KAAA,OAAAxM,GAAA,WAAAyM,IAAA;QAAA,OAAAA,IAAA,CAAAnR,IAAA;MAAA,GAAAoR,MAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAA/M,MAAA;MAAA;MACAvE,OAAA,CAAAC,GAAA,SAAAmR,KAAA,CAAA7M,MAAA;MAEA,IAAA6M,KAAA,CAAA7M,MAAA;QACAvE,OAAA,CAAA+L,IAAA;QACA;UAAA5C,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;MAEAtJ,OAAA,CAAAC,GAAA,WAAAmR,KAAA,CAAAI,KAAA;MAEA,IAAAC,oBAAA;MACA,IAAAC,cAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;QACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;QAEA;QACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAP,IAAA,UAAAQ,mBAAA,CAAAR,IAAA;QAEA,IAAAM,eAAA;UACA;UACA,IAAAH,oBAAA,CAAAlN,MAAA;YACA;cACA,IAAAwN,YAAA,GAAAN,oBAAA,CAAAO,IAAA;cACAhS,OAAA,CAAAC,GAAA,0CAAAyE,MAAA,CAAAgN,cAAA,0BAAAK,YAAA;cACA,IAAAE,cAAA,QAAAC,sBAAA,CAAAH,YAAA,EAAAL,cAAA;cACA,IAAAO,cAAA;gBAAA,IAAAE,qBAAA,EAAAC,qBAAA;gBACAjJ,SAAA,CAAApD,IAAA,CAAAkM,cAAA;gBACAjS,OAAA,CAAAC,GAAA,oCAAAyE,MAAA,CAAAyE,SAAA,CAAA5E,MAAA;kBACAP,IAAA,EAAAiO,cAAA,CAAAtT,YAAA;kBACAqR,OAAA,IAAAmC,qBAAA,GAAAF,cAAA,CAAApT,eAAA,cAAAsT,qBAAA,uBAAAA,qBAAA,CAAAjS,SAAA;kBACAmS,OAAA,IAAAD,qBAAA,GAAAH,cAAA,CAAAI,OAAA,cAAAD,qBAAA,uBAAAA,qBAAA,CAAA7N,MAAA;kBACA+N,MAAA,EAAAL,cAAA,CAAAM;gBACA;cACA;YACA,SAAAnQ,KAAA;cACAkH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAAgN,cAAA,uCAAAhN,MAAA,CAAAtC,KAAA,CAAA+O,OAAA;cACAnR,OAAA,CAAAoC,KAAA,wBAAAsC,MAAA,CAAAgN,cAAA,iCAAAtP,KAAA;YACA;UACA;;UAEA;UACAqP,oBAAA,IAAAH,IAAA;UACAI,cAAA;UACA1R,OAAA,CAAAC,GAAA,0CAAAyE,MAAA,CAAAgN,cAAA,QAAAJ,IAAA;QACA;UACA;UACA,IAAAG,oBAAA,CAAAlN,MAAA;YACAkN,oBAAA,CAAA1L,IAAA,CAAAuL,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAG,oBAAA,CAAAlN,MAAA;QACA;UACA,IAAAwN,aAAA,GAAAN,oBAAA,CAAAO,IAAA;UACA,IAAAC,eAAA,QAAAC,sBAAA,CAAAH,aAAA,EAAAL,cAAA;UACA,IAAAO,eAAA;YAAA,IAAAO,sBAAA;YACArJ,SAAA,CAAApD,IAAA,CAAAkM,eAAA;YACAjS,OAAA,CAAAC,GAAA,gDAAAyE,MAAA,CAAAyE,SAAA,CAAA5E,MAAA,UAAAiO,sBAAA,GAAAP,eAAA,CAAApT,eAAA,cAAA2T,sBAAA,uBAAAA,sBAAA,CAAAtS,SAAA;UACA;QACA,SAAAkC,KAAA;UACAkH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAAgN,cAAA,uCAAAhN,MAAA,CAAAtC,KAAA,CAAA+O,OAAA;UACAnR,OAAA,CAAAoC,KAAA,oCAAAsC,MAAA,CAAAgN,cAAA,iCAAAtP,KAAA;QACA;MACA;IAEA,SAAAA,KAAA;MACAkH,MAAA,CAAAvD,IAAA,0CAAArB,MAAA,CAAAtC,KAAA,CAAA+O,OAAA;MACAnR,OAAA,CAAAoC,KAAA,cAAAA,KAAA;IACA;IAEApC,OAAA,CAAAC,GAAA,WAAAkJ,SAAA,CAAA5E,MAAA,UAAA+E,MAAA,CAAA/E,MAAA;IACA;MAAA4E,SAAA,EAAAA,SAAA;MAAAG,MAAA,EAAAA;IAAA;EACA,oCAGAuI,oBAAAP,IAAA;IACA;IACA;IACA;IACA,wBAAAmB,IAAA,CAAAnB,IAAA;EACA,oCAGAQ,oBAAAR,IAAA;IACA;IACA;IACA,mBAAAmB,IAAA,CAAAnB,IAAA;EACA,uCAGAY,uBAAAH,YAAA;IACA,IAAAX,KAAA,GAAAW,YAAA,CAAAV,KAAA,OAAAxM,GAAA,WAAAyM,IAAA;MAAA,OAAAA,IAAA,CAAAnR,IAAA;IAAA,GAAAoR,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA/M,MAAA;IAAA;IAEA,IAAA6M,KAAA,CAAA7M,MAAA;MACA,UAAAmH,KAAA;IACA;IAEA,IAAA/M,YAAA;IACA,IAAAE,eAAA;IACA,IAAA6T,iBAAA;;IAEA;IACA,SAAAf,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;MACA,IAAAgB,SAAA,GAAArB,IAAA,CAAAsB,KAAA;MACA,IAAAD,SAAA;QACA,IAAAE,QAAA,GAAAF,SAAA;QACA3S,OAAA,CAAAC,GAAA,eAAA4S,QAAA;;QAEA;QACA,IAAAA,QAAA,CAAA/M,QAAA;UACAnH,YAAA;QACA,WAAAkU,QAAA,CAAA/M,QAAA;UACAnH,YAAA;QACA,WAAAkU,QAAA,CAAA/M,QAAA;UACAnH,YAAA;QACA,WAAAkU,QAAA,CAAA/M,QAAA;UACAnH,YAAA;QACA,WAAAkU,QAAA,CAAA/M,QAAA;UACAnH,YAAA;QACA;QAEAqB,OAAA,CAAAC,GAAA,gBAAAtB,YAAA;;QAEA;QACA,IAAAmU,gBAAA,GAAAxB,IAAA,CAAAvK,OAAA,iBAAA5G,IAAA;QACA,IAAA2S,gBAAA;UACAjU,eAAA,GAAAiU,gBAAA;UACAJ,iBAAA,GAAAf,CAAA;UACA3R,OAAA,CAAAC,GAAA,kBAAA6S,gBAAA;QACA;UACAJ,iBAAA,GAAAf,CAAA;UACA3R,OAAA,CAAAC,GAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,IAAAyS,iBAAA;MACAA,iBAAA;MACA;MACA/T,YAAA,QAAAoU,iBAAA,CAAA3B,KAAA;MACApR,OAAA,CAAAC,GAAA,sBAAAtB,YAAA;IACA;;IAEA;IACA,SAAAgT,EAAA,GAAAe,iBAAA,EAAAf,EAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,EAAA;MACA,IAAAL,KAAA,GAAAF,KAAA,CAAAO,EAAA;;MAEA;MACA,SAAAE,mBAAA,CAAAP,KAAA;QACA;QACAzS,eAAA,GAAAyS,KAAA,CAAAvK,OAAA,uBAAA5G,IAAA;QACAuS,iBAAA,GAAAf,EAAA;QACA;MACA,YAAA9S,eAAA;QACA;QACAA,eAAA,GAAAyS,KAAA;QACAoB,iBAAA,GAAAf,EAAA;QACA;MACA;IACA;;IAEA;IACA,SAAAA,GAAA,GAAAe,iBAAA,EAAAf,GAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,GAAA;MACA,IAAAL,MAAA,GAAAF,KAAA,CAAAO,GAAA;;MAEA;MACA,SAAAqB,YAAA,CAAA1B,MAAA,UAAA2B,YAAA,CAAA3B,MAAA,KACA,KAAA4B,iBAAA,CAAA5B,MAAA,UAAA6B,gBAAA,CAAA7B,MAAA;QACA;MACA;;MAEA;MACA,IAAA8B,SAAA,GAAA9B,MAAA;MACA;MACA,SAAAO,mBAAA,CAAAP,MAAA;QACA8B,SAAA,GAAA9B,MAAA,CAAAvK,OAAA,uBAAA5G,IAAA;MACA;MAEA,IAAAiT,SAAA;QACA,IAAAvU,eAAA;UACAA,eAAA,WAAAuU,SAAA;QACA;UACAvU,eAAA,GAAAuU,SAAA;QACA;MACA;IACA;IAEApT,OAAA,CAAAC,GAAA,mEAAApB,eAAA;IAEA,KAAAA,eAAA;MACA,UAAA6M,KAAA;IACA;;IAEA;IACA,IAAA2H,oBAAA,GAAAxU,eAAA,CAAAsB,IAAA;IACA;IACA,wBAAAsS,IAAA,CAAAY,oBAAA;MACAA,oBAAA,GAAAA,oBAAA,CAAAtM,OAAA,0BAAA5G,IAAA;IACA;;IAEA;IACA,IAAAkT,oBAAA,CAAAvN,QAAA;MACAuN,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;IACA;IAEA,IAAAhN,QAAA;MACA1H,YAAA,EAAAA,YAAA;MACAqF,IAAA,EAAArF,YAAA;MACA4U,QAAA,OAAAC,kBAAA,CAAA7U,YAAA;MACAE,eAAA,EAAAwU,oBAAA;MACArD,OAAA,EAAAqD,oBAAA;MACAzU,UAAA;MAAA;MACA6U,WAAA;MACApB,OAAA;MACAE,aAAA;MACAnJ,SAAA;IACA;IAEApJ,OAAA,CAAAC,GAAA;MACA+D,IAAA,EAAArF,YAAA;MACA8K,eAAA,EAAA5K,eAAA,CAAAsB,IAAA;MACAuT,YAAA,EAAAL,oBAAA;MACAM,iBAAA,gBAAAlB,IAAA,CAAAY,oBAAA;IACA;;IAEA;IACA,IAAAO,YAAA,QAAAC,qBAAA,CAAAzC,KAAA;IACA/K,QAAA,CAAAgM,OAAA,GAAAuB,YAAA,CAAAvB,OAAA;;IAEA;IACA,IAAA1T,YAAA,mBAAA0H,QAAA,CAAAgM,OAAA,CAAA9N,MAAA;MACA;MACA5F,YAAA;MACA0H,QAAA,CAAA1H,YAAA,GAAAA,YAAA;MACA0H,QAAA,CAAArC,IAAA,GAAArF,YAAA;MACA0H,QAAA,CAAAkN,QAAA,QAAAC,kBAAA,CAAA7U,YAAA;MACAqB,OAAA,CAAAC,GAAA,oBAAAtB,YAAA;IACA;;IAEA;IACA,KAAAmV,0BAAA,CAAA1C,KAAA,EAAA/K,QAAA;;IAEA;IACA,IAAA1H,YAAA,iBAAA0H,QAAA,CAAAkM,aAAA,IAAAlM,QAAA,CAAAkM,aAAA,CAAAhO,MAAA;MACA;MACA,kBAAAkO,IAAA,CAAApM,QAAA,CAAAkM,aAAA;QACA5T,YAAA;QACA0H,QAAA,CAAA1H,YAAA,GAAAA,YAAA;QACA0H,QAAA,CAAArC,IAAA,GAAArF,YAAA;QACA0H,QAAA,CAAAkN,QAAA,QAAAC,kBAAA,CAAA7U,YAAA;QACAqB,OAAA,CAAAC,GAAA,sBAAAtB,YAAA;MACA;IACA;;IAEA;IACA0H,QAAA,CAAAxH,eAAA,QAAAyU,oBAAA,CAAAjN,QAAA,CAAAxH,eAAA;IACAwH,QAAA,CAAA2J,OAAA,GAAA3J,QAAA,CAAAxH,eAAA;IAEA,OAAAwH,QAAA;EACA,6BAGA2M,aAAA1B,IAAA;IACA;IACA,6BAAAmB,IAAA,CAAAnB,IAAA;EACA,6BAGA2B,aAAA3B,IAAA;IACA;IACA,sBAAAmB,IAAA,CAAAnB,IAAA;EACA,kCAGA4B,kBAAA5B,IAAA;IACA;IACA,sBAAAmB,IAAA,CAAAnB,IAAA;EACA,iCAGA6B,iBAAA7B,IAAA;IACA;IACA,sBAAAmB,IAAA,CAAAnB,IAAA;EACA,mCAGAkC,mBAAAxP,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA;EACA,kCAGA+P,kBAAA/D,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACAhQ,OAAA,CAAAC,GAAA,qBAAA+P,OAAA,CAAA9P,SAAA;;MAEA;MACA,IAAA8T,gBAAA,GAAAhE,OAAA,CAAAjJ,OAAA,mDAAA6L,KAAA,EAAAqB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,SAAAtB,KAAA;QAEA5S,OAAA,CAAAC,GAAA,mBAAAiU,GAAA;;QAEA;QACA,IAAAA,GAAA,CAAAhG,UAAA,eAAAgG,GAAA,CAAAhG,UAAA,gBAAAgG,GAAA,CAAAhG,UAAA;UACA,OAAA0E,KAAA;QACA;;QAEA;QACA,IAAAwB,OAAA,8BAAAF,GAAA,CAAAhG,UAAA,QAAAgG,GAAA,SAAAA,GAAA;QACA,IAAAG,MAAA,UAAA3P,MAAA,CAAAuP,MAAA,YAAAvP,MAAA,CAAA0P,OAAA,QAAA1P,MAAA,CAAAyP,KAAA;QACAnU,OAAA,CAAAC,GAAA,kBAAAoU,MAAA;QACA,OAAAA,MAAA;MACA;MAEA,OAAAL,gBAAA;IACA,SAAA5R,KAAA;MACApC,OAAA,CAAAoC,KAAA,iBAAAA,KAAA;MACA,OAAA4N,OAAA;IACA;EACA,QAAAhR,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,yCAGAoR,2BAAAoB,OAAA;IAAA,IAAAsE,OAAA;IACA,KAAAtE,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA;MACA,IAAAgE,gBAAA,GAAAhE;MACA;MAAA,CACAjJ,OAAA,oDAAA6L,KAAA,EAAAqB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,CAAAhG,UAAA,aAAAgG,GAAA,CAAAhG,UAAA;UACA,IAAAkG,OAAA,GAAAE,OAAA,CAAAP,iBAAA,CAAAG,GAAA;UACA,cAAAxP,MAAA,CAAAuP,MAAA,YAAAvP,MAAA,CAAA0P,OAAA,QAAA1P,MAAA,CAAAyP,KAAA;QACA;QACA,OAAAvB,KAAA;MACA;MACA;MAAA,CACA7L,OAAA,sBACAA,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAA,OAAA,sBACAA,OAAA;MAEA,OAAAiN,gBAAA,CAAA7T,IAAA;IACA,SAAAiC,KAAA;MACApC,OAAA,CAAAoC,KAAA,qCAAAA,KAAA;MACA,OAAA4N,OAAA;IACA;EACA,wCAGAnB,wBAAAmB,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACAhQ,OAAA,CAAAC,GAAA,sCAAA+P,OAAA,CAAAzL,MAAA;;MAEA;MACA,IAAAgQ,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,uBAAA,GAAAzE,OAAA,CAAAjJ,OAAA,2BAAA6L,KAAA;QACA5S,OAAA,CAAAC,GAAA,gBAAA2S,KAAA,CAAA1S,SAAA;QACAqU,MAAA,CAAAxO,IAAA,CAAA6M,KAAA;QACA,gCAAAlO,MAAA,CAAA8P,UAAA;MACA;;MAEA;MACA,IAAA1E,WAAA,GAAA2E,uBAAA,CACA1N,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;;MAEA;MACA,IAAA2M,YAAA,GAAA5D,WAAA;MACAyE,MAAA,CAAA/Q,OAAA,WAAAkR,GAAA,EAAA1O,KAAA;QACA,IAAAqJ,WAAA,0BAAA3K,MAAA,CAAAsB,KAAA;QACA,IAAA0N,YAAA,CAAA5N,QAAA,CAAAuJ,WAAA;UACAqE,YAAA,GAAAA,YAAA,CAAA3M,OAAA,CAAAsI,WAAA,EAAAqF,GAAA;QACA;MACA;MAEA1U,OAAA,CAAAC,GAAA,yCAAAyT,YAAA,CAAAnP,MAAA;MACA,OAAAmP,YAAA,CAAAvT,IAAA;IACA,SAAAiC,KAAA;MACApC,OAAA,CAAAoC,KAAA,kCAAAA,KAAA;MACA,OAAA4N,OAAA;IACA;EACA,sCAGA6D,sBAAAzC,KAAA,EAAAuD,UAAA;IACA,IAAAtC,OAAA;IAEA,KAAA7B,KAAA,CAAAoE,OAAA,CAAAxD,KAAA,KAAAuD,UAAA,QAAAA,UAAA,IAAAvD,KAAA,CAAA7M,MAAA;MACAvE,OAAA,CAAA+L,IAAA;MACA;QAAAsG,OAAA,EAAAA;MAAA;IACA;IAEA;MACA,SAAAV,CAAA,GAAAgD,UAAA,EAAAhD,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;QACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;QAEA,KAAAL,IAAA,WAAAA,IAAA;UACA;QACA;;QAEA;QACA,IAAAuD,WAAA,GAAAvD,IAAA,CAAAsB,KAAA;QACA,IAAAiC,WAAA;UACA,IAAAC,SAAA,GAAAD,WAAA,IAAAE,WAAA;UACA,IAAAC,aAAA,GAAAH,WAAA,MAAAA,WAAA,IAAA1U,IAAA;UAEA,IAAA2U,SAAA,IAAAE,aAAA;YACA3C,OAAA,CAAAtM,IAAA;cACA+O,SAAA,EAAAA,SAAA;cACAG,KAAA,EAAAH,SAAA;cACAE,aAAA,EAAAA,aAAA;cACAhF,OAAA,EAAAgF;YACA;UACA;QACA,gBAAA/B,YAAA,CAAA3B,IAAA,UAAA4B,iBAAA,CAAA5B,IAAA,UAAA6B,gBAAA,CAAA7B,IAAA;UACA;UACA;QACA;UACA;UACA;UACA,IAAA4D,oBAAA,GAAA5D,IAAA,CAAAsB,KAAA;UACA,IAAAsC,oBAAA;YACA;YACA,IAAAC,aAAA,GAAA7D,IAAA,CAAAD,KAAA;YAAA,IAAA+D,SAAA,OAAAC,2BAAA,CAAApW,OAAA,EACAkW,aAAA;cAAAG,KAAA;YAAA;cAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAhK,CAAA,IAAAhE,IAAA;gBAAA,IAAAoO,YAAA,GAAAF,KAAA,CAAAhG,KAAA;gBACA,KAAAkG,YAAA;gBAEA,IAAA5C,KAAA,GAAA4C,YAAA,CAAA5C,KAAA;gBACA,IAAAA,KAAA;kBACA,IAAAkC,UAAA,GAAAlC,KAAA,IAAAmC,WAAA;kBACA,IAAAC,cAAA,GAAApC,KAAA,MAAAA,KAAA,IAAAzS,IAAA;kBAEA,IAAA2U,UAAA,IAAAE,cAAA;oBACA3C,OAAA,CAAAtM,IAAA;sBACA+O,SAAA,EAAAA,UAAA;sBACAG,KAAA,EAAAH,UAAA;sBACAE,aAAA,EAAAA,cAAA;sBACAhF,OAAA,EAAAgF;oBACA;kBACA;gBACA;cACA;YAAA,SAAAS,GAAA;cAAAL,SAAA,CAAA/G,CAAA,CAAAoH,GAAA;YAAA;cAAAL,SAAA,CAAAM,CAAA;YAAA;UACA;QACA;MACA;IACA,SAAAtT,KAAA;MACApC,OAAA,CAAAoC,KAAA,eAAAA,KAAA;IACA;IAEA;MAAAiQ,OAAA,EAAAA;IAAA;EACA,2CAGAyB,2BAAA1C,KAAA,EAAA/K,QAAA;IACA,SAAAsL,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;MAEA;MACA,IAAAgE,WAAA,GAAArE,IAAA,CAAAsB,KAAA;MACA,IAAA+C,WAAA;QACAtP,QAAA,CAAAkM,aAAA,QAAAqD,gBAAA,CAAAD,WAAA,KAAAtP,QAAA,CAAA1H,YAAA;QACA;MACA;;MAEA;MACA,IAAAkX,gBAAA,GAAAvE,IAAA,CAAAsB,KAAA;MACA,IAAAiD,gBAAA;QACAxP,QAAA,CAAAoN,WAAA,GAAAoC,gBAAA,IAAA1V,IAAA;QACA;MACA;;MAEA;MACA,IAAA2V,eAAA,GAAAxE,IAAA,CAAAsB,KAAA;MACA,IAAAkD,eAAA;QACA,IAAAlX,UAAA,GAAAkX,eAAA;QACA;QACA,IAAAlX,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAkH,QAAA,CAAAlH,UAAA;UACAyH,QAAA,CAAAzH,UAAA,GAAAA,UAAA;UACAoB,OAAA,CAAAC,GAAA,cAAArB,UAAA;QACA;UACAoB,OAAA,CAAA+L,IAAA,iBAAAnN,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA;IACA,KAAAyH,QAAA,CAAAkM,aAAA;MACAlM,QAAA,CAAAkM,aAAA,QAAAwD,gCAAA,CAAA1P,QAAA,CAAAxH,eAAA,EAAAwH,QAAA,CAAA1H,YAAA;IACA;EACA,iDAGAoX,iCAAAlX,eAAA,EAAAF,YAAA;IACA,KAAAE,eAAA,WAAAA,eAAA;MACA;IACA;IAEA;MACA;MACA,IAAAmX,QAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAA3R,MAAA,EAAA0R,GAAA;QAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,GAAA;QACA,IAAAG,OAAA,GAAAvX,eAAA,CAAA+T,KAAA,CAAAuD,OAAA;QACA,IAAAC,OAAA,IAAAA,OAAA,CAAA7R,MAAA;UACA;UACA,IAAA8R,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAA7R,MAAA;UACA,IAAA+N,MAAA,GAAA+D,SAAA,CAAAtP,OAAA,sBAAA5G,IAAA;UAEA,IAAAmS,MAAA;YACA,YAAAsD,gBAAA,CAAAtD,MAAA,EAAA3T,YAAA;UACA;QACA;MACA;IACA,SAAAyD,KAAA;MACApC,OAAA,CAAAoC,KAAA,kBAAAA,KAAA;IACA;IAEA;EACA,iCAGAwT,iBAAAU,UAAA,EAAA3X,YAAA;IACA,KAAA2X,UAAA,WAAAA,UAAA;MACA;IACA;IAEA;MACA,IAAAC,aAAA,GAAAD,UAAA,CAAAnW,IAAA;MAEA,KAAAoW,aAAA;QACA;MACA;MAEA,IAAA5X,YAAA;QACA;QACA,OAAA4X,aAAA;MACA;QACA;QACA,OAAAA,aAAA,CAAAxB,WAAA;MACA;IACA,SAAA3S,KAAA;MACApC,OAAA,CAAAoC,KAAA,gBAAAA,KAAA;MACA,OAAAkU,UAAA;IACA;EACA,oCAGAE,oBAAAxG,OAAA;IACA,IAAAyG,QAAA;IACA,IAAAC,SAAA;IAEA,IAAAC,SAAA;IACA,IAAA/D,KAAA;IACA,IAAAgE,WAAA;IAEA,QAAAhE,KAAA,GAAA8D,SAAA,CAAAG,IAAA,CAAA7G,OAAA;MACA,IAAA4G,WAAA;QACA;QACAH,QAAA,CAAA1Q,IAAA;UACA/B,IAAA,EAAA4S,WAAA;UACA5G,OAAA,EAAAA,OAAA,CAAA9P,SAAA,CAAAyW,SAAA,EAAA/D,KAAA,CAAA5M,KAAA,EAAA7F,IAAA;QACA;MACA;MACAyW,WAAA,GAAAhE,KAAA;MACA+D,SAAA,GAAA/D,KAAA,CAAA5M,KAAA,GAAA4M,KAAA,IAAArO,MAAA;IACA;;IAEA;IACA,IAAAqS,WAAA;MACAH,QAAA,CAAA1Q,IAAA;QACA/B,IAAA,EAAA4S,WAAA;QACA5G,OAAA,EAAAA,OAAA,CAAA9P,SAAA,CAAAyW,SAAA,EAAAxW,IAAA;MACA;IACA;IAEA,OAAAsW,QAAA;EACA,sCAGAK,sBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,IAAA7N,SAAA;IACA,IAAAxK,YAAA,QAAAsY,mBAAA,CAAAF,OAAA,CAAA/S,IAAA;;IAEA;IACA,IAAAkT,cAAA,QAAAC,qBAAA,CAAAJ,OAAA,CAAA/G,OAAA;IAEAkH,cAAA,CAAA1T,OAAA,WAAA4T,KAAA,EAAApR,KAAA;MACA;QACA,IAAAK,QAAA,GAAA2Q,OAAA,CAAAK,kBAAA,CAAAD,KAAA,EAAAzY,YAAA,EAAAqH,KAAA;QACA,IAAAK,QAAA;UACA8C,SAAA,CAAApD,IAAA,CAAAM,QAAA;QACA;MACA,SAAAjE,KAAA;QACA,UAAAsJ,KAAA,UAAAhH,MAAA,CAAAsB,KAAA,0CAAAtB,MAAA,CAAAtC,KAAA,CAAA+O,OAAA;MACA;IACA;IAEA,OAAAhI,SAAA;EACA,sCAGAgO,sBAAAnH,OAAA;IACA,IAAAsH,MAAA;IACA,IAAAC,WAAA;IAEA,IAAAZ,SAAA;IACA,IAAA/D,KAAA;IAEA,QAAAA,KAAA,GAAA2E,WAAA,CAAAV,IAAA,CAAA7G,OAAA;MACA,IAAA2G,SAAA;QACA;QACAW,MAAA,CAAAvR,IAAA,CAAAiK,OAAA,CAAA9P,SAAA,CAAAyW,SAAA,EAAA/D,KAAA,CAAA5M,KAAA,EAAA7F,IAAA;MACA;MACAwW,SAAA,GAAA/D,KAAA,CAAA5M,KAAA;IACA;;IAEA;IACA,IAAA2Q,SAAA,GAAA3G,OAAA,CAAAzL,MAAA;MACA+S,MAAA,CAAAvR,IAAA,CAAAiK,OAAA,CAAA9P,SAAA,CAAAyW,SAAA,EAAAxW,IAAA;IACA;IAEA,OAAAmX,MAAA,CAAA/F,MAAA,WAAA6F,KAAA;MAAA,OAAAA,KAAA,CAAA7S,MAAA;IAAA;EACA,mCAGA8S,mBAAAD,KAAA,EAAAzY,YAAA;IACA,IAAAyS,KAAA,GAAAgG,KAAA,CAAA/F,KAAA,OAAAxM,GAAA,WAAAyM,IAAA;MAAA,OAAAA,IAAA,CAAAnR,IAAA;IAAA,GAAAoR,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA/M,MAAA;IAAA;IAEA,IAAA6M,KAAA,CAAA7M,MAAA;MACA,UAAAmH,KAAA;IACA;;IAEA;IACA,IAAA8L,SAAA,GAAApG,KAAA;IACA,IAAAvS,eAAA;IACA,IAAA4Y,gBAAA;;IAEA;IACA,IAAAC,WAAA,GAAAF,SAAA,CAAA5E,KAAA;IACA,IAAA8E,WAAA;MACA7Y,eAAA,GAAA6Y,WAAA,IAAAvX,IAAA;MACAsX,gBAAA;IACA;MACA;MACA5Y,eAAA,QAAAyU,oBAAA,CAAAkE,SAAA,EAAArX,IAAA;MACAsX,gBAAA;IACA;;IAEA;IACA,OAAAA,gBAAA,GAAArG,KAAA,CAAA7M,MAAA;MACA,IAAA+M,IAAA,GAAAF,KAAA,CAAAqG,gBAAA;MACA,SAAAzE,YAAA,CAAA1B,IAAA;QACA;MACA;MACAzS,eAAA,WAAAyS,IAAA;MACAmG,gBAAA;IACA;IAEA,IAAApR,QAAA;MACA1H,YAAA,EAAAA,YAAA;MACAE,eAAA,EAAAA,eAAA,CAAAsB,IAAA;MACAvB,UAAA;MAAA;MACA6U,WAAA;MACApB,OAAA;MACAE,aAAA;IACA;;IAEA;IACA,IAAA5T,YAAA;MACA,IAAAiV,YAAA,QAAA+D,YAAA,CAAAvG,KAAA,EAAAqG,gBAAA;MACApR,QAAA,CAAAgM,OAAA,GAAAuB,YAAA,CAAAvB,OAAA;MACAoF,gBAAA,GAAA7D,YAAA,CAAAgE,SAAA;IACA;;IAEA;IACA,KAAAC,iBAAA,CAAAzG,KAAA,EAAAqG,gBAAA,EAAApR,QAAA;;IAEA;IACAA,QAAA,CAAAxH,eAAA,QAAAyU,oBAAA,CAAAjN,QAAA,CAAAxH,eAAA;IAEA,OAAAwH,QAAA;EACA,QAAArH,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,2BAAAwV,aAGA1B,IAAA;IACA,4BAAAmB,IAAA,CAAAnB,IAAA;EACA,6BAGAqG,aAAAvG,KAAA,EAAAuD,UAAA;IACA,IAAAtC,OAAA;IACA,IAAAyF,YAAA,GAAAnD,UAAA;IAEA,OAAAmD,YAAA,GAAA1G,KAAA,CAAA7M,MAAA;MACA,IAAA+M,IAAA,GAAAF,KAAA,CAAA0G,YAAA;MACA,IAAAjD,WAAA,GAAAvD,IAAA,CAAAsB,KAAA;MAEA,KAAAiC,WAAA;QACA;MACA;MAEAxC,OAAA,CAAAtM,IAAA;QACA+O,SAAA,EAAAD,WAAA,IAAAE,WAAA;QACAC,aAAA,EAAAH,WAAA,IAAA1U,IAAA;MACA;MAEA2X,YAAA;IACA;IAEA;MAAAzF,OAAA,EAAAA,OAAA;MAAAuF,SAAA,EAAAE;IAAA;EACA,kCAGAD,kBAAAzG,KAAA,EAAAuD,UAAA,EAAAtO,QAAA;IACA,SAAAsL,CAAA,GAAAgD,UAAA,EAAAhD,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;MAEA;MACA,IAAAgE,WAAA,GAAArE,IAAA,CAAAsB,KAAA;MACA,IAAA+C,WAAA;QACAtP,QAAA,CAAAkM,aAAA,QAAAwF,WAAA,CAAApC,WAAA,KAAAtP,QAAA,CAAA1H,YAAA;QACA;MACA;;MAEA;MACA,IAAAkX,gBAAA,GAAAvE,IAAA,CAAAsB,KAAA;MACA,IAAAiD,gBAAA;QACAxP,QAAA,CAAAoN,WAAA,GAAAoC,gBAAA,IAAA1V,IAAA;QACA;MACA;;MAEA;MACA,IAAA2V,eAAA,GAAAxE,IAAA,CAAAsB,KAAA;MACA,IAAAkD,eAAA;QACA,IAAAlX,UAAA,GAAAkX,eAAA;QACA;QACA,IAAAlX,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAkH,QAAA,CAAAlH,UAAA;UACAyH,QAAA,CAAAzH,UAAA,GAAAA,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,KAAAyH,QAAA,CAAAkM,aAAA;MACAlM,QAAA,CAAAkM,aAAA,QAAAyF,wBAAA,CAAA3R,QAAA,CAAAxH,eAAA,EAAAwH,QAAA,CAAA1H,YAAA;IACA;EACA,yCAKAqZ,yBAAAhI,OAAA,EAAArR,YAAA;IACA;IACA,IAAAsZ,eAAA,IACA,cACA,iBACA,cACA,eACA;IAEA,SAAAC,GAAA,MAAAC,gBAAA,GAAAF,eAAA,EAAAC,GAAA,GAAAC,gBAAA,CAAA5T,MAAA,EAAA2T,GAAA;MAAA,IAAA/B,OAAA,GAAAgC,gBAAA,CAAAD,GAAA;MACA,IAAA9B,OAAA,OAAA9K,mBAAA,CAAArM,OAAA,EAAA+Q,OAAA,CAAAoI,QAAA,CAAAjC,OAAA;MACA,IAAAC,OAAA,CAAA7R,MAAA;QACA,IAAA+N,MAAA,GAAA8D,OAAA,CAAAA,OAAA,CAAA7R,MAAA;QACA,YAAAwT,WAAA,CAAAzF,MAAA,EAAA3T,YAAA;MACA;IACA;IAEA;EACA,oCAGAsY,oBAAApE,QAAA;IACA,IAAAzP,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAyP,QAAA;EACA,oCAGAwF,oBAAArU,IAAA;IACA,IAAAZ,OAAA,OAAApE,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,QAEA,aACA,aACA,MACA;;IAEA;IACAe,OAAA,CAAAC,GAAA,YAAA+D,IAAA,aAAAsU,QAAA,CAAArZ,OAAA,EAAA+E,IAAA;;IAEA;IACA,IAAAA,IAAA,aAAAA,IAAA,KAAAN,SAAA,IAAAM,IAAA,WAAAZ,OAAA,CAAAY,IAAA,MAAAN,SAAA;MACA1D,OAAA,CAAAC,GAAA;MACA;IACA;IAEA,IAAAoU,MAAA,GAAAjR,OAAA,CAAAY,IAAA;IACAhE,OAAA,CAAAC,GAAA,eAAAoU,MAAA;IACA,OAAAA,MAAA;EACA,wCAGAkE,wBAAAlS,QAAA;IACA,KAAAA,QAAA;MACA;IACA;;IAEA;IACA,IAAA1H,YAAA,GAAA0H,QAAA,CAAA1H,YAAA;;IAEA;IACA,KAAAA,YAAA,IAAA0H,QAAA,CAAArC,IAAA;MACArF,YAAA,GAAA0H,QAAA,CAAArC,IAAA;IACA;;IAEA;IACA,KAAArF,YAAA;MACA;MACA,IAAA0H,QAAA,CAAAgM,OAAA,IAAAhM,QAAA,CAAAgM,OAAA,CAAA9N,MAAA;QACA;QACA,IAAA8B,QAAA,CAAAkM,aAAA,IAAAlM,QAAA,CAAAkM,aAAA,CAAAhO,MAAA,sBAAAkO,IAAA,CAAApM,QAAA,CAAAkM,aAAA;UACA5T,YAAA;QACA;UACAA,YAAA;QACA;MACA;QACA;QACA,IAAA0H,QAAA,CAAAkM,aAAA,wCAAAE,IAAA,CAAApM,QAAA,CAAAkM,aAAA;UACA5T,YAAA;QACA;UACAA,YAAA;QACA;MACA;IACA;IAEA,YAAA0Z,mBAAA,CAAA1Z,YAAA;EACA,kCAGAoU,kBAAA3B,KAAA;IACA,KAAAA,KAAA,IAAAA,KAAA,CAAA7M,MAAA;MACA;IACA;IAEA,IAAAiU,UAAA;IACA,IAAAC,WAAA;IACA,IAAAC,mBAAA;;IAEA;IAAA,IAAAC,UAAA,OAAAtD,2BAAA,CAAApW,OAAA,EACAmS,KAAA;MAAAwH,MAAA;IAAA;MAAA,KAAAD,UAAA,CAAApD,CAAA,MAAAqD,MAAA,GAAAD,UAAA,CAAAvN,CAAA,IAAAhE,IAAA;QAAA,IAAAkK,IAAA,GAAAsH,MAAA,CAAAtJ,KAAA;QACA,SAAA0D,YAAA,CAAA1B,IAAA;UACAkH,UAAA;UACAC,WAAA;QACA;;QAEA;QACA,IAAAnH,IAAA,CAAAxL,QAAA,UAAAwL,IAAA,CAAAxL,QAAA,UACAwL,IAAA,CAAAxL,QAAA,SAAAwL,IAAA,CAAAxL,QAAA,SACAwL,IAAA,CAAAxL,QAAA,SAAAwL,IAAA,CAAAxL,QAAA,SACA,6CAAA2M,IAAA,CAAAnB,IAAA;UACAoH,mBAAA;QACA;MACA;;MAEA;IAAA,SAAAjD,GAAA;MAAAkD,UAAA,CAAAtK,CAAA,CAAAoH,GAAA;IAAA;MAAAkD,UAAA,CAAAjD,CAAA;IAAA;IACA,IAAAgD,mBAAA;MACA;IACA;;IAEA;IACA,IAAAF,UAAA;MACA;MACA,IAAAC,WAAA;QACA;MACA,WAAAA,WAAA;QACA;MACA;IACA;;IAEA;IACA;EACA,qCAGAI,qBAAA7U,IAAA;IACA,IAAA8U,QAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,QAAA,CAAA9U,IAAA;EACA,kCAKA1C,kBAAA;IACA;MACA,IAAAyX,UAAA,GAAAC,YAAA,CAAAC,OAAA,MAAAC,QAAA;MACA,IAAAH,UAAA;QACA,IAAAjb,IAAA,GAAAqb,IAAA,CAAAC,KAAA,CAAAL,UAAA;QACA/Y,OAAA,CAAAC,GAAA,eAAAnC,IAAA;;QAEA;QACA,KAAA8B,eAAA,GAAA9B,IAAA,CAAA8B,eAAA;QACA,KAAA+J,mBAAA,GAAA7L,IAAA,CAAA6L,mBAAA;QACA,KAAAC,YAAA,GAAA9L,IAAA,CAAA8L,YAAA;;QAEA;QACA,SAAAhK,eAAA,SAAA+J,mBAAA;UACA,KAAAmF,iBAAA;UACA9O,OAAA,CAAAC,GAAA;QACA;MACA;IACA,SAAAmC,KAAA;MACApC,OAAA,CAAAoC,KAAA,gBAAAA,KAAA;IACA;EACA,QAAApD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,0BAGAuR,YAAA;IAAA,IAAAsK,OAAA;IACA;IACA,SAAAzX,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;IAEA,KAAAA,aAAA,GAAAsH,UAAA;MACAmQ,OAAA,CAAA1X,cAAA;IACA;EACA,+BAGAA,eAAA;IACA;MACA,IAAA2X,UAAA;QACA1Z,eAAA,OAAAA,eAAA;QACA+J,mBAAA,OAAAA,mBAAA;QACAC,YAAA,OAAAA,YAAA,QAAAC,IAAA,GAAAC,cAAA;QACAyP,SAAA,EAAA1P,IAAA,CAAA2P,GAAA;MACA;MAEAR,YAAA,CAAAS,OAAA,MAAAP,QAAA,EAAAC,IAAA,CAAAO,SAAA,CAAAJ,UAAA;MACAtZ,OAAA,CAAAC,GAAA;MACA,KAAA6O,iBAAA;IACA,SAAA1M,KAAA;MACApC,OAAA,CAAAoC,KAAA,eAAAA,KAAA;IACA;EACA,2BAGAuJ,WAAA;IACA;MACAqN,YAAA,CAAAW,UAAA,MAAAT,QAAA;MACA,KAAApK,iBAAA;MACA9O,OAAA,CAAAC,GAAA;IACA,SAAAmC,KAAA;MACApC,OAAA,CAAAoC,KAAA,cAAAA,KAAA;IACA;EACA,mCAGAX,mBAAAmY,KAAA;IACA,SAAA9K,iBAAA;MACA;MACA,KAAAnN,cAAA;;MAEA;MACA,IAAAwP,OAAA;MACAyI,KAAA,CAAAC,WAAA,GAAA1I,OAAA;MACA,OAAAA,OAAA;IACA;EACA,2BAGA2I,WAAA;IACA,KAAAnY,cAAA;IACA,KAAAQ,QAAA,CAAA6C,OAAA;EACA,mCAGA+U,mBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,QAAAD,OAAA;MACA;QACA,KAAA7U,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACArB,IAAA;QACA,GAAAlB,IAAA;UACAmX,OAAA,CAAAtO,UAAA;UACAsO,OAAA,CAAAra,eAAA;UACAqa,OAAA,CAAAtQ,mBAAA;UACAsQ,OAAA,CAAArQ,YAAA;;UAEA;UACA,IAAAqQ,OAAA,CAAArZ,UAAA,IAAAqZ,OAAA,CAAAnZ,iBAAA;YACAmZ,OAAA,CAAArZ,UAAA,CAAAwH,OAAA;UACA;UAEA6R,OAAA,CAAA9X,QAAA,CAAA6C,OAAA;QACA,GAAA/B,KAAA;UACA;QAAA,CACA;QACA;MACA;QACA,KAAAiX,WAAA;QACA;IACA;EACA,4BAGAA,YAAA;IACA,UAAAvQ,mBAAA,UAAA/J,eAAA;MACA,KAAAuC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,IAAAwL,OAAA,QAAArG,mBAAA,SAAA/J,eAAA;IACA,IAAAua,IAAA,OAAAC,IAAA,EAAApK,OAAA;MAAAhM,IAAA;IAAA;IACA,IAAAqW,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;IACA,IAAAK,IAAA,GAAAtO,QAAA,CAAAiD,aAAA;IACAqL,IAAA,CAAAC,IAAA,GAAAJ,GAAA;IACAG,IAAA,CAAAlS,QAAA,+BAAA5D,MAAA,KAAAmF,IAAA,GAAA6Q,WAAA,GAAAlJ,KAAA,QAAAzK,OAAA;IACAmF,QAAA,CAAAyO,IAAA,CAAAjL,WAAA,CAAA8K,IAAA;IACAA,IAAA,CAAAI,KAAA;IACA1O,QAAA,CAAAyO,IAAA,CAAAE,WAAA,CAAAL,IAAA;IACAF,GAAA,CAAAQ,eAAA,CAAAT,GAAA;IAEA,KAAAlY,QAAA,CAAA6C,OAAA;EACA,4CAKA+V,4BAAA1U,QAAA;IACA,KAAAA,QAAA,KAAAA,QAAA,CAAAxH,eAAA;MACA;IACA;IAEA,IAAAmR,OAAA,GAAA3J,QAAA,CAAAxH,eAAA;;IAEA;IACA,SAAA8K,mBAAA,SAAAA,mBAAA,CAAA7D,QAAA;MACA;MACA,IAAAkV,WAAA,QAAAC,uBAAA,CAAA5U,QAAA,CAAAxH,eAAA,OAAA8K,mBAAA;MACA,IAAAqR,WAAA;QACAhL,OAAA,GAAAgL,WAAA;MACA;IACA;;IAEA;IACAhL,OAAA,QAAAsD,oBAAA,CAAAtD,OAAA;;IAEA;IACAA,OAAA,QAAAkL,sBAAA,CAAAlL,OAAA;IAEA,YAAA+D,iBAAA,CAAA/D,OAAA;EACA,uCAGAkL,uBAAAlL,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAAlK,QAAA;MACA,OAAAkK;MACA;MAAA,CACAjJ,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACA5G,IAAA;IACA;MACA;MACA,OAAA6P;MACA;MAAA,CACAjJ,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACA5G,IAAA;IACA;EACA,qCAGAmT,qBAAAtD,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAAlK,QAAA;MACA;MACA,OAAAkK,OAAA,CAAAjJ,OAAA,wDACAA,OAAA;MAAA,CACAA,OAAA;IACA;MACA;MACA,OAAAiJ,OAAA,CAAAjJ,OAAA,0BAAA5G,IAAA;IACA;EACA,QAAAnB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,sCAGAyd,wBAAAE,YAAA,EAAAH,WAAA;IACA,KAAAG,YAAA,KAAAH,WAAA;MACA,OAAAG,YAAA;IACA;IAEA;MACA;MACA,IAAAC,SAAA,GAAAD,YAAA,CAAApU,OAAA,uBAAA5G,IAAA;;MAEA;MACA,IAAAkb,UAAA,GAAAL,WAAA,CAAApI,KAAA;MAAA,IAAA0I,UAAA,OAAAjG,2BAAA,CAAApW,OAAA,EAEAoc,UAAA;QAAAE,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA/F,CAAA,MAAAgG,MAAA,GAAAD,UAAA,CAAAlQ,CAAA,IAAAhE,IAAA;UAAA,IAAAoU,SAAA,GAAAD,MAAA,CAAAjM,KAAA;UACA,IAAAmM,aAAA,GAAAD,SAAA,CAAAzU,OAAA,iBAAA5G,IAAA;UACA;UACA,IAAAub,kBAAA,GAAAD,aAAA,CAAA1U,OAAA,0BAAA5G,IAAA;UACA,IAAAub,kBAAA,CAAA5V,QAAA,CAAAsV,SAAA,CAAAlb,SAAA;YACA;YACA,YAAAoT,oBAAA,CAAAkI,SAAA;UACA;QACA;;QAEA;MAAA,SAAA/F,GAAA;QAAA6F,UAAA,CAAAjN,CAAA,CAAAoH,GAAA;MAAA;QAAA6F,UAAA,CAAA5F,CAAA;MAAA;MACA,OAAAyF,YAAA;IACA,SAAA/Y,KAAA;MACApC,OAAA,CAAAoC,KAAA,kBAAAA,KAAA;MACA,OAAA+Y,YAAA;IACA;EACA,6BAIAQ,aAAA;IACA,KAAAnd,WAAA,CAAAC,OAAA;IACA,KAAA6D,eAAA;EACA,4BAEAsZ,YAAA;IACA,KAAApd,WAAA,CAAAG,YAAA;IACA,KAAAH,WAAA,CAAAI,UAAA;IACA,KAAAJ,WAAA,CAAAK,eAAA;IACA,KAAAL,WAAA,CAAAC,OAAA;IACA,KAAA6D,eAAA;EACA;AAEA", "ignoreList": []}]}