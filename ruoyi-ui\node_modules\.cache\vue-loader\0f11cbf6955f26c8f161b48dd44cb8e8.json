{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=template&id=5888aa98&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}